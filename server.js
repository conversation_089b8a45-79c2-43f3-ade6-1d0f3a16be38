
const express = require('express');
const cors = require('cors');
const path = require('path');
const axios = require('axios');
const CryptoJS = require('crypto-js');
require('dotenv').config();
const { GoogleGenerativeAI } = require('@google/generative-ai');


const app = express();
const PORT = process.env.PORT || 8080;

// Encryption key - 32 bytes / 256 bits
const ENCRYPTION_KEY = process.env.ENCRYPTION_KEY || 'K8x#mP9$vL2@nQ5&jR7*hT4^wY3!cF6';

// Enable CORS
app.use(cors());

// Parse JSON bodies
app.use(express.json());

// Serve static files from the root directory
app.use(express.static(path.join(__dirname, '/')));



const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
console.log('process.env.GEMINI_API_KEY', process.env.GEMINI_API_KEY)
const model = genAI.getGenerativeModel({ model: "gemini-2.0-flash" })

// Event Tracker endpoint
app.post('/api/event_tracker', async (req, res) => {
    try {
        const response = await axios.post('https://careplan-v2-r1-apr15-564097302460.us-central1.run.app/event_tracker', req.body, {
            headers: {
                'Content-Type': 'application/json'
            }
        });
        res.json(response.data);
    } catch (error) {
        console.error('Event Tracker Error:', error.response?.data || error.message);
        res.status(500).json({
            error: 'Failed to track event',
            details: error.response?.data || error.message
        });
    }
});

// Care Gaps endpoint
app.post('/api/care_gaps_by_ehrid', async (req, res) => {
    try {
        const response = await axios.post('https://careplan-v2-r1-apr15-564097302460.us-central1.run.app/care_gaps_by_ehrid', req.body, {
            headers: {
                'Content-Type': 'application/json'
            }
        });
        res.json(response.data);
    } catch (error) {
        console.error('Care Gaps Error:', error.response?.data || error.message);
        res.status(500).json({
            error: 'Failed to fetch care gaps',
            details: error.response?.data || error.message
        });
    }
});

// Get Care Plan endpoint
app.get('/api/get-careplan', async (req, res) => {
    try {
        const { patient_id } = req.query;
        if (!patient_id) {
            return res.status(400).json({ error: 'Patient ID is required' });
        }

        const response = await axios.get(`https://careplan-v2-r1-apr15-564097302460.us-central1.run.app/get-careplan`, {
            params: { patient_id },
            headers: {
                'Content-Type': 'application/json'
            }
        });
        res.json(response.data);
    } catch (error) {
        console.error('Get Care Plan Error:', error.response?.data || error.message);
        res.status(500).json({
            error: 'Failed to fetch care plan',
            details: error.response?.data || error.message
        });
    }
});

// Get Patient Info endpoint
app.get('/userinfoapi/get-patinfo', async (req, res) => {
    try {
        const { pid } = req.query;
        if (!pid) {
            return res.status(400).json({ error: 'Patient ID is required' });
        }

        const response = await axios.get(`https://chwrapperservice-564097302460.us-central1.run.app/get-patinfo`, {
            params: { pid },
            headers: {
                'Content-Type': 'application/json',
                'apiKey': 'O20f6ceb-SGWhmw-4078-Casdg2'
            }
        });
        res.json(response.data);
    } catch (error) {
        console.error('Get Patient Info Error:', error.response?.data || error.message);
        res.status(500).json({
            error: 'Failed to fetch patient info',
            details: error.response?.data || error.message
        });
    }
});

// Link shorten
app.post('/shorten-link', async (req, res) => {
    // Define the URL for the POST request
    const API_KEY = "9119701155092073209b58ec77e5a48c";
    const url = "https://nao.md/generate";
    const { desc } = req.body;
    const { link } = req.body;
    // Change this URL as needed

    // Define the headers
    const headers = {
        "Content-Type": "application/json",
        "X-API-KEY": API_KEY
    };

    // Define the data to send with the POST request
    const data = {
        destination: link,
        short_url: desc
    };

    console.log(data)

    try {
        console.log(`> requesting to generate short link : ${url}`);
        // Send the POST request
        const apiResponse = await axios.post(url, data, { headers });

        // Print the response status code and JSON response
        console.log(`Status Code: ${apiResponse.status}`);
        console.log(`Response:`, apiResponse.data);
        const responseData = apiResponse.data;
        res.json(responseData);
    } catch (error) {
        // Handle error
        if (error.response) {
            console.error(`Error Status Code: ${error.response.status}`);
            console.error(`Error Response:`, error.response.data);
        } else {
            console.error(`Error: ${error.message}`);
        }

        return { "message": "sorry-no-data-available", "error": error };

    }
});

// Send invite route
app.post('/send-sms', async (req, res) => {
    try {
        const { contactNo } = req.body;
        const { message } = req.body;

        console.log("Phone number", contactNo);

        const url = "https://us-central1-care-plan-beta.cloudfunctions.net/nao-communications/send-message";
        const response = await axios.post(url, {
            "phoneNum": contactNo,
            "smsText": message
        }, {
            headers: {
                'Content-Type': 'application/json'
            }
        });

        console.log("Send SMS", response.data);
        res.json({ status: "sent" });
    } catch (error) {
        console.error('Error sending SMS:', error.response?.data || error.message);
        
        // Check for specific bad_request error from Podium API
        if (error.response?.data?.code === 'bad_request' && 
            error.response?.data?.message?.includes('phone_number')) {
            return res.status(400).json({
                error: 'Invalid phone number',
                message: 'The provided phone number is not valid or properly formatted',
                code: 'bad_request',
                details: error.response.data
            });
        }
        
        // Generic error handling for other cases
        res.status(500).json({
            error: 'Failed to send SMS',
            details: error.response?.data || error.message
        });
    }
});

// Encryption endpoint
app.post('/encrypt', (req, res) => {
    try {
        const { data } = req.body;
        if (!data) {
            return res.status(400).json({ error: 'No data provided' });
        }

        const encrypted = CryptoJS.AES.encrypt(data, ENCRYPTION_KEY).toString();
        res.json({ encrypted });
    } catch (error) {
        console.error('Encryption error:', error);
        res.status(500).json({ error: 'Encryption failed' });
    }
});

// Decryption endpoint
app.post('/decrypt', (req, res) => {
    try {
        const { encrypted } = req.body;
        if (!encrypted) {
            return res.status(400).json({ error: 'No encrypted data provided' });
        }

        const decrypted = CryptoJS.AES.decrypt(encrypted, ENCRYPTION_KEY).toString(CryptoJS.enc.Utf8);
        res.json({ decrypted });
    } catch (error) {
        console.error('Decryption error:', error);
        res.status(500).json({ error: 'Decryption failed' });
    }
});



function extractTextForSimplification(jsonData, context) {
    const texts = [];
    if (context === 'care_gaps' && jsonData.careGaps) {
        jsonData.careGaps.forEach((gap, index) => {
            texts.push({
                id: `careGap_${index}`,
                text: gap.description
            });
        });
    } else if (context === 'care_plan') {
        const plan = jsonData.care_plan?.patient_facing_plan;
        if (plan) {
            plan.behavioral_goals?.forEach((goal, goalIndex) => {
                goal.item.forEach((itemText, itemIndex) => {
                    texts.push({
                        id: `behavioral_${goalIndex}_${itemIndex}`,
                        text: itemText
                    });
                });
            });
            plan.education_instructions?.forEach((instr, instrIndex) => {
                instr.item.forEach((itemText, itemIndex) => {
                    texts.push({
                        id: `education_${instrIndex}_${itemIndex}`,
                        text: itemText
                    });
                });
            });
        }
    }
    return texts;
}







// NEW: Create the API endpoint that the frontend will call
// NEW: Endpoint for batch JSON processing
app.post('/ai/simplify-json', express.json(), async (req, res) => {
    try {
        const { json_data, context } = req.body;
        if (!json_data || !context) {
            return res.status(400).json({ error: 'Missing "json_data" or "context"' });
        }

        const textsToSimplify = extractTextForSimplification(json_data, context);

        if (textsToSimplify.length === 0) {
            return res.json([]);
        }

        // The prompt remains the same
        // const newPrompt = `
        //     You are an AI assistant specializing in patient communication.
        //     Your goal is to translate medical text into simple, clear, and encouraging messages.
        //     Use everyday language (5th-grade reading level), be concise (4-10 words), and maintain a positive tone.

        //     You will receive a JSON array of objects, each with an "id" and a "text" field.
        //     For each object, rewrite the "text" value according to the rules above.
        //     Return a valid JSON array with the exact same "id" for each object, but with the rewritten text in a new field called "simplified_text".

        //     EXAMPLE:
        //     - Input: [{"id": "careGap_0", "text": "Patient is overdue for a biennial mammogram screening."}]
        //     - Your Output: [{"id": "careGap_0", "simplified_text": "A friendly reminder to schedule your mammogram."}]

        //     JSON to process:
        //     ${JSON.stringify(textsToSimplify)}
        // `;

        const newPrompt = `
You are an AI assistant specializing in patient communication. Your goal is to translate complex medical instructions into simple, clear, and encouraging messages that are easy for anyone to understand.

**Critical Rule: You MUST incorporate the specific medical condition or reason mentioned in the input into your simplified output.** The patient must understand WHY they need to take action. Do not discard this context.

Follow these rules for each item:
1.  **Preserve Context:** The simplified text must clearly mention the condition (e.g., "Hypothyroidism," "Type 1 Diabetes," "Liver Health").
2.  **Simple Language:** Use everyday words (5th-grade reading level).
3.  **Be Concise:** Aim for 5-12 words. Be brief, but not at the expense of clarity.
4.  **Positive Tone:** Be encouraging and supportive.

---
**EXAMPLES of what to do:**

*   **Input:** [{"id": "careGap_0", "text": "Patient is overdue for a biennial mammogram screening."}]
*   **Your Output:** [{"id": "careGap_0", "simplified_text": "A friendly reminder to schedule your mammogram."}]

*   **Input:** [{"id": "liver_1", "text": "Unspecified Fatty Liver Disease: Blood work"}]
*   **Your Output:** [{"id": "liver_1", "simplified_text": "Time for a blood test to check on your liver."}]

*   **Input:** [{"id": "thyroid_2", "text": "Hypothyroidism: Blood Work"}]
*   **Your Output:** [{"id": "thyroid_2", "simplified_text": "Let's check your thyroid levels with a blood test."}]

*   **Input:** [{"id": "t1d_3", "text": "Type 1 Diabetes: A1c Test"}]
*   **Your Output:** [{"id": "t1d_3", "simplified_text": "Time for your regular Type 1 Diabetes A1c test."}]

---
**WHAT TO AVOID (Bad Examples):**

*   **AVOID THIS:** Turning both "Hypothyroidism: Blood Work" and "Fatty Liver: Blood work" into the same generic message like "A blood test is needed." This loses critical context.
*   **AVOID THIS:** Turning both "Type 1 Diabetes: A1c Test" and "Type 2 Diabetes: A1c Test" into "Time for your A1c test." The patient needs to see their specific condition mentioned.

---
JSON to process:
${JSON.stringify(textsToSimplify)}
`;

        const generationConfig = {
            response_mime_type: "application/json",
        };

        let result;
        try {
            const model = genAI.getGenerativeModel({ model: "gemini-1.5-flash" });
            result = await model.generateContent(newPrompt, generationConfig);
        } catch (apiError) {
            console.error("!!! ERROR DURING GEMINI API CALL !!!", apiError);
            return res.status(500).json([]);
        }

        const response = await result.response;
        const simplifiedJsonString = response.text();

        // =========================================================================
        // --- NEW & FINAL FIX: Use a Regular Expression to EXTRACT the JSON ---
        // This is far more reliable than trying to trim the start and end.
        // It finds the first '[' and grabs everything until the last ']'.
        // The 's' flag allows '.' to match newline characters.
        // =========================================================================
        const match = simplifiedJsonString.match(/\[.*\]/s);

        if (!match) {
            console.error("Could not find a valid JSON array (`[...]`) in the AI's response.");
            console.error("Full AI response:", simplifiedJsonString);
            return res.status(500).json([]);
        }

        // The extracted JSON is the first (and only) match in the array.
        const extractedJson = match[0];
        // =========================================================================

        let simplifiedResults;
        try {
            // Now, we parse the GUARANTEED CLEAN string.
            simplifiedResults = JSON.parse(extractedJson);
        } catch (parseError) {
            console.error("The extracted JSON block was still invalid:", parseError);
            console.error("Malformed block that was extracted:", extractedJson);
            return res.status(500).json([]); // Fallback
        }

        res.json(simplifiedResults);

    } catch (error) {
        console.error("A critical unexpected error occurred in /ai/simplify-json:", error);
        res.status(500).json([]); // Fallback
    }
});


app.post('/api/sendotp', async (req, res) => {
    const phoneNumber = req.body.phoneNum;
    const url = "https://us-central1-care-plan-beta.cloudfunctions.net/nao-communications/send-otp";
    const response = await axios.post(url, { phoneNum: phoneNumber });

    console.log("sendOTP request sent successfully.", phoneNumber);
    res.json(response.data);
    console.log('response-----------------', response.data)

})



app.post('/api/verifyotp', async (req, res) => {
    const phoneNumber = req.body.phoneNum;
    const otp = req.body.otp
    const url = "https://us-central1-care-plan-beta.cloudfunctions.net/nao-communications/verify-otp";
    try {
        const response = await axios.post(url, { phoneNum: phoneNumber, otp: otp });

        console.log('Response from external API:', response.data);


        if (response.data && response.data.error || response.data.status === "invalid-otp") {

            console.log("Verification FAILED for:", phoneNumber, "Reason:", response.data.message);

            res.status(400).json(response.data);
        } else {

            console.log("Verification successful for:", phoneNumber);

            res.json(response.data);
        }
    } catch (error) {

        console.error("Server error calling the external OTP service:", error.message);
        res.status(500).json({ message: 'Error verifying OTP', error: 'Internal Server Error' });
    }

})



// Patient education route
app.get('/patient-education', (req, res) => {
    res.sendFile(path.join(__dirname, 'patient-education.html'));
});

// Serve index.html for all other routes
app.get('*', (req, res) => {
    res.sendFile(path.join(__dirname, 'index.html'));
});

app.listen(PORT, () => {
    console.log(`Server running on port ${PORT}`);
});
