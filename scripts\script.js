// Google Auth variables
let tokenClient;
const appContent = document.getElementById('app-content');
const loginSection = document.getElementById('login-section');
const customSigninBtn = document.getElementById('custom-signin-btn');
const profileBtn = document.getElementById('profile-btn');
const profilePopup = document.getElementById('profile-popup');
const userNameElement = document.getElementById('user-name');
let userName = '';
let firstName;
let lastName;
let patientId;
let email;
let phoneNumber;

// Add these global variables to store selected provider details
let selectedProviderId = null;
let selectedProviderName = null;
let selectedLocationId = null;
let selectedPatientId = null;
let selectedLocationName = null;
let selectedDate = null;

const locMap = [
    {
        locationName: "AST",
        id: [233163, 233164],
        text: "Astoria",
    },
    {
        locationName: "BRTW",
        id: [233165, 233166],
        text: "Bronx Bartow",
    },
    {
        locationName: "BX174",
        text: "Bronx 174th St",
        id: [233167, 233168],
    },
    {
        locationName: "CH",
        text: "Crown Heights",
        id: [233170, 233171],
    },
    {
        locationName: "DWNT",
        text: "StuyTown",
        id: [233172, 233192],
    },
    {
        id: [233174],
        text: "Hicksville",
        locationName: "HIX",
    },
    {
        id: [233175, 233176],
        text: "Jackson Heights",
        locationName: "JH",
    },
    {
        text: "Jamaica",
        id: [233177, 233178],
        locationName: "JAM",
    },
    {
        text: "Long Island City",
        id: [233179, 233193],
        locationName: "LIC",
    },
    {
        id: [233183, 233194],
        text: "Mineola",
        locationName: "MIN",
    },
    {
        text: "Williamsburg",
        id: [233195, 233213],
        locationName: "WIL",
    }
];

// Add the new Google Identity Services script
document.write('<script src="https://accounts.google.com/gsi/client" async defer></script>');

async function trackEvent(eventName, eventCategory, eventLabel) {
    const payload = {
        "event-name": eventName,
        "event_category": eventCategory,
        "event_label": eventLabel,
        "patient_acct_no": patientId,
        "providerName": firstName + " " + lastName,
        "providerId": selectedProviderId,
        "userName": userName,
        "email": email
    };

    const response = await fetch('/api/event_tracker', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload)
    });
}

// Initialize Google Identity Services when the page loads
document.addEventListener('DOMContentLoaded', function () {
    const shareBtn = document.querySelector('.share-btn');

    if (shareBtn) {
        shareBtn.classList.add('skeleton');
        shareBtn.disabled = true;
    }

    function generateRandomString() {
        const length = 8;
        const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz1234567890';
        let result = '';

        for (let i = 0; i < length; i++) {
            const randomIndex = Math.floor(Math.random() * characters.length);
            result += characters[randomIndex];
        }

        return result;
    }

    async function shortenLink(link) {
        const descString = generateRandomString();
        try {
            console.log(`\t>> requesting linkshorten ...`);
            const linkInfo = { "link": link, "desc": descString };
            const response = await fetch('/shorten-link', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(linkInfo)
            });

            mockResponse = await response.json();

            return mockResponse["short_url"];

        } catch (error) {
            console.error("Failed to fetch the report:", error);
        }
    }

    if (shareBtn) {
        shareBtn.addEventListener("click", async function () {
            if (!shareBtn.classList.contains("disabled")) {
                shareBtn.innerText = "Sending...";
                shareBtn.classList.add("disabled");
                shareBtn.disabled = true;

                try {
                    // First encrypt the patient ID
                    const encryptResponse = await fetch('/encrypt', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ data: patientId })
                    });

                    const { encrypted } = await encryptResponse.json();

                    // Use the encrypted ID in the URL
                    const shortenURL = await shortenLink(`https://careplan-extension-v2-564097302460.us-central1.run.app/patient-education?pid=${encodeURIComponent(encrypted)}`);
                    // const shortenURL = await shortenLink(`http://localhost:8080/patient-education?pid=${encodeURIComponent(encrypted)}`);


                    if (phoneNumber != null) {
                        const cleanPhoneNumber = phoneNumber ? phoneNumber.replace(/-/g, '') : null;
                        const contactNo = cleanPhoneNumber;
                        const message = `Hi ${firstName + ' ' + lastName} Your personalized care plan by Dr. Sandeep Jain is ready. Click the link below to view your tailored recommendations and start your journey toward better health today!: ${shortenURL}`

                        const response = await fetch('/send-sms', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({ contactNo, message })
                        });

                        const data = await response.json();

                        shareBtn.innerText = "Share with patient";
                        shareBtn.classList.remove("disabled");
                        shareBtn.disabled = false;

                        if (data.error) {
                            alert('Error sending invite: ' + data.error);
                        }
                    } else {
                        shareBtn.disabled = true;
                        shareBtn.innerText = "No phone number found";
                        shareBtn.classList.add("disabled");
                    }
                } catch (error) {
                    console.error('Error:', error);
                    alert('Failed to send Invite');
                }
            }
        });
    }

    // Close dropdown when clicking outside
    document.addEventListener('click', function (event) {
        if (event.target.classList.contains('selected-provider')) {
            document.querySelector(".provider-dropdown").classList.toggle('active');
        }
        if (!event.target.closest('.provider-container')) {
            document.querySelector(".provider-dropdown").classList.remove('active');
        }
    });

    // Check for existing session
    checkExistingSession();

    // Set up profile button click handler
    profileBtn.addEventListener('click', function () {
        profilePopup.classList.toggle('active');
    });

    // Check if Google API is loaded
    if (typeof google !== 'undefined') {
        initializeGoogleAuth();
    } else {
        // If not loaded yet, wait for it
        window.onGoogleLibraryLoad = initializeGoogleAuth;
    }

    // Main variables
    let carePlanData = {};
    const tooltipElement = document.getElementById('tooltip');
    const tooltipTitleElement = document.querySelector('#tooltip .tooltip-header h3');
    const tooltipTextElement = document.getElementById('tooltip-text');
    const loadingElement = document.getElementById('loading');
    const carePlanContentElement = document.getElementById('care-plan-content');
    const searchForm = document.querySelector('.search-form');

    window.addEventListener('message', function (event) {
        // Verify the sender origin for security
        if (event.origin !== "https://nysuccapp.eclinicalweb.com") return;

        // Access the account number
        const accountNumber = event.data.accountNumber;

        document.getElementById("patient-id-input").value = accountNumber;

        // Fetch appointments with the account number
        fetchPatientAppointments(accountNumber);

        // Trigger form submission after setting the account number
        searchForm.dispatchEvent(new Event('submit'));
    });

    // Add form submit handler
    searchForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        
        if (shareBtn) {
            shareBtn.classList.add('skeleton');
            shareBtn.disabled = true;
        }

        loadingElement.style.display = 'block';
        carePlanContentElement.style.display = 'none';

        const patientIdInput = document.getElementById('patient-id-input');
        patientId = patientIdInput ? patientIdInput.value : '';

        // Enable share button only if patientId exists
        if (patientId != null || patientId != "") {
            if (shareBtn) {
                shareBtn.classList.remove('skeleton');
                shareBtn.disabled = false;
            }
        }

        fetchCareGaps();

        // Fetch both care plan and appointments data
        fetchCarePlanData();

        await fetchPatientAppointments(patientId);


        await trackEvent("patient careplan search", "search", `provider searched for ${patientId}`);
    });

    // Set up event listeners
    setupTooltipListeners();

    /**
     * Fetch care plan data from the Flask API
     */
    async function fetchCarePlanData() {
        try {
            const patientIdInput = document.getElementById('patient-id-input');
            const patientId = patientIdInput ? patientIdInput.value : '';

            const url = `/api/get-careplan?patient_id=${patientId}`;

            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            if (!response.ok) {
                const error = new Error(`HTTP error! Status: ${response.status}`);
                error.status = response.status;
                throw error;
            }

            carePlanData = await response.json();

            // Render data to UI components
            renderFlaggedPriorities();
            renderProviderFacingPlan();
            renderPatientFacingPlan();
            renderFollowUpTimeline();
            fetchPatientInfo();

            // Hide loading, show content
            loadingElement.style.display = 'none';
            carePlanContentElement.style.display = 'block';

            // Set up accordion listeners after content is rendered
            setupAccordionListeners();

        } catch (error) {
            console.error('Error fetching care plan data:', error);

            // Check if it's a 500 error (Internal Server Error)
            if (error.status === 500) {
                loadingElement.innerHTML = `<p class="error">No care plan found for this patient</p>`;
            }
            else if(error.details && error.details.error){
                loadingElement.innerHTML = `<p class="error">${error.details.error}</p>`;
            }
            else{
                loadingElement.innerHTML = `<p class="error">Error loading care plan data. Please try again.</p>`;
            }
        }
    }

    async function fetchPatientInfo() {
        const patientIdInput = document.getElementById('patient-id-input');
        const patientId = patientIdInput ? patientIdInput.value : '';

        const patientValues = document.querySelector('.patient-value');

        patientValues.classList.remove('skeleton');
        patientValues.innerHTML = "Patient: " + `<b>${patientId}</b>`;

        // Get all patient-value elements
        const patientName = document.querySelector('.patient-name');

        // Add skeleton class to all patient-value elements
        patientName.innerHTML = "";
        patientName.classList.add('skeleton');

        const patInfoUrl = `/userinfoapi/get-patinfo?pid=${patientId}`;
        const patInfoResponse = await fetch(patInfoUrl);
        const responseText = await patInfoResponse.json();
        
        let sanitizedResponse;
        let patientInfo;

        if(typeof responseText === "string"){
            // Replace NaN with null before parsing
            sanitizedResponse = responseText.replace(/:\s*NaN/g, ': null');
            patientInfo = JSON.parse(sanitizedResponse);
        }
        else{
            sanitizedResponse = responseText;
            patientInfo = sanitizedResponse;
        }

        phoneNumber = patientInfo.data.patient_cell_phone || patientInfo.data.patient_home_phone || patientInfo.data.patient_work_phone || null;

        firstName = patientInfo.data.patient_first_name.charAt(0).toUpperCase() +
            patientInfo.data.patient_first_name.slice(1).toLowerCase();
        lastName = patientInfo.data.patient_last_name.charAt(0).toUpperCase() +
            patientInfo.data.patient_last_name.slice(1).toLowerCase();

        const gender = patientInfo.data.patient_gender.charAt(0).toUpperCase() +
            patientInfo.data.patient_gender.slice(1).toLowerCase();

        const dob = patientInfo.data.patient_dob_string;
        const birthDate = new Date(dob);
        const today = new Date();
        let age = today.getFullYear() - birthDate.getFullYear();
        const monthDiff = today.getMonth() - birthDate.getMonth();

        if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
            age--;
        }

        document.querySelector('.patient-gender').textContent = gender + ", ";
        document.querySelector('.patient-age').textContent = age + " y.o";
        document.querySelector('.gen-age-wrapper').classList.remove('skeleton');

        // Update the name display and remove skeleton
        patientName.textContent = `${firstName} ${lastName}`;
        patientName.classList.remove('skeleton');
    }

    async function fetchCareGaps() {
        const payload = {
            "ehrId": patientId,
        };

        const response = await fetch('/api/care_gaps_by_ehrid', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(payload)
        });

        if (!response.ok) {
            console.error('Error fetching care gaps:', response.status);
            return;
        }

        const careGapsData = await response.json();

        if (careGapsData.careGaps.length != 0) {
            renderCareGaps(careGapsData.careGaps);
        } else {
            if (document.querySelector(".caregaps")) {
                document.querySelector(".caregaps").remove();
            }
        }
    }

    /**
     * Render care gaps section in accordion format
     */
    function renderCareGaps(careGaps) {
        const container = document.getElementById('care-plan-content');
        if (!container) return;

        // Add style for accordion spacing
        const style = document.createElement('style');
        style.textContent = `
            .caregaps .subfield {
                margin-bottom: 12px;
            }
            .caregaps .subfield:last-child {
                margin-bottom: 0;
            }
        `;
        document.head.appendChild(style);

        // Remove existing care gaps section if it exists
        const existingCareGaps = container.querySelector('.accordion-section h2');
        if (existingCareGaps && existingCareGaps.textContent === 'Care Gaps') {
            existingCareGaps.closest('.accordion-section').remove();
        }

        // Create care gaps accordion section
        const careGapsAccordion = document.createElement('div');
        careGapsAccordion.className = 'accordion-section caregaps';

        const header = document.createElement('div');
        header.className = 'accordion-header';
        header.innerHTML = `<h2>Care Gaps</h2>
                        <span class="toggle-icon"> <i class="fas fa-chevron-down"></i></span>`;
        careGapsAccordion.appendChild(header);

        const content = document.createElement('div');
        content.className = 'accordion-content';

        // Create containers for each confidence level
        const confidenceLevels = {
            'CONFIRMED': { title: 'Confirmed', items: [] },
            'HIGH': { title: 'High', items: [] },
            'MEDIUM': { title: 'Medium', items: [] },
        };

        // Sort care gaps into their respective confidence levels
        careGaps.forEach(gap => {
            if (confidenceLevels[gap.confidenceLevel]) {
                confidenceLevels[gap.confidenceLevel].items.push(gap);
            }
        });

        // Create accordions for each confidence level
        Object.entries(confidenceLevels).forEach(([level, data]) => {
            if (data.items.length > 0) {
                const levelAccordion = document.createElement('div');
                levelAccordion.className = 'caregap-section';

                const levelHeader = document.createElement('div');
                levelHeader.className = 'caregap-title';
                levelHeader.innerHTML = `<h3>${data.title}</h3>`;
                levelAccordion.appendChild(levelHeader);

                const levelContent = document.createElement('div');
                levelContent.className = 'caregaps';

                const pairsContainer = document.createElement('div');
                pairsContainer.className = 'value-reason-pairs';

                data.items.forEach(gap => {
                    const pair = document.createElement('div');
                    pair.className = 'value-reason-pair';

                    // Format the care gap text
                    const formattedGap = gap.description;

                    // Create the main text content
                    let innerHTML = `<span class="value-text">${formattedGap}</span>${(gap.possibleICDCodes && gap.possibleICDCodes.length > 0) || (gap.possibleCPTCodes && gap.possibleCPTCodes.length > 0) ? '<span class="info-icon"></span>' : ""}`;
                    pair.innerHTML = innerHTML;

                    // Create tooltip content
                    if ((gap.possibleICDCodes && gap.possibleICDCodes.length > 0) || (gap.possibleCPTCodes && gap.possibleCPTCodes.length > 0)) {
                        let tooltipContent = gap.description;

                        if (gap.possibleICDCodes && gap.possibleICDCodes.length > 0) {
                            tooltipContent += '<br><br>ICD Codes:';
                            gap.possibleICDCodes.forEach(code => {
                                tooltipContent += `<br>${code.value} - ${code.description}`;
                            });
                        }

                        if (gap.possibleCPTCodes && gap.possibleCPTCodes.length > 0) {
                            tooltipContent += '<br><br>CPT Codes:';
                            gap.possibleCPTCodes.forEach(code => {
                                tooltipContent += `<br>${code.value} - ${code.description}`;
                            });
                        }

                        // Add hover event listeners
                        pair.addEventListener('mouseenter', (event) => {
                            const rect = event.currentTarget.getBoundingClientRect();
                            const top = rect.top;
                            const left = rect.left;
                            showTooltip('Details', tooltipContent, top, left);
                        });

                        pair.addEventListener('mouseleave', () => {
                            hideTooltip();
                        });
                    }

                    pairsContainer.appendChild(pair);
                });

                levelContent.appendChild(pairsContainer);
                levelAccordion.appendChild(levelContent);
                content.appendChild(levelAccordion);

                // Add accordion toggle listener
                if (!levelHeader.dataset.listenerAttached) {
                    levelHeader.addEventListener('click', (event) => {
                        event.stopPropagation();
                        levelAccordion.classList.toggle('active');
                    });
                    levelHeader.dataset.listenerAttached = 'true';
                }
            }
        });

        careGapsAccordion.appendChild(content);
        container.prepend(careGapsAccordion);

        // Add accordion toggle listener
        if (!header.dataset.listenerAttached) {
            header.addEventListener('click', (event) => {
                event.stopPropagation();
                careGapsAccordion.classList.toggle('active');
            });
            header.dataset.listenerAttached = 'true';
        }
    }

    /**
     * Set up event listeners for accordion toggles
     */
    function setupAccordionListeners() {
        // Setup main accordion headers (High Risk, Provider Care Plan, etc.)
        document.querySelectorAll('.accordion-container > .accordion-section > .accordion-header').forEach(header => {
            if (header.dataset.listenerAttached) return;

            header.addEventListener('click', () => {
                const section = header.parentElement;
                section.classList.toggle('active');

                trackEvent("Viewed", "Accordion clicked", `User viewed ${header.querySelector("h2").textContent}`);
            });
            header.dataset.listenerAttached = 'true';
        });

        // Setup subfield headers (Provider Plan sections)
        document.querySelectorAll('.subfield > .accordion-header').forEach(header => {
            if (header.dataset.listenerAttached) return;

            header.addEventListener('click', (event) => {
                event.stopPropagation();
                const subfield = header.parentElement;
                subfield.classList.toggle('active');

                trackEvent("Viewed sub category", "Sub accordion clicked", `User viewed ${header.querySelector("h2").textContent}`);
            });
            header.dataset.listenerAttached = 'true';
        });

        // NOTE: Listeners for Patient Education subsections are added directly in renderPatientFacingPlan
    }


    /**
     * Set up event listeners for tooltip/reason display
     */
    function setupTooltipListeners() {
        // Close tooltip when clicking the close button
        document.querySelector('.close-tooltip').addEventListener('click', () => {
            tooltipElement.style.display = 'none';
        });

        // Close tooltip when clicking outside the tooltip content
        tooltipElement.addEventListener('click', (event) => {
            if (event.target === tooltipElement) {
                tooltipElement.style.display = 'none';
            }
        });

        // Close tooltip with Escape key
        document.addEventListener('keydown', (event) => {
            if (event.key === 'Escape' && tooltipElement.style.display === 'flex') {
                tooltipElement.style.display = 'none';
            }
        });
    }

    // --- MODIFICATION START ---
    /**
     * Show tooltip with dynamic title and content text
     * @param {string} titleText - The title to display (e.g., "Reason", "Education")
     * @param {string} contentText - The main text content for the tooltip
     */
    function showTooltip(titleText, contentText, top, left) {
        if (!tooltipTitleElement) {
            console.error("Tooltip title element not found!");
            return;
        }

        tooltipTitleElement.innerHTML = titleText;
        tooltipTextElement.innerHTML = contentText;
        tooltipElement.style.display = 'flex';

        // Get tooltip's height after content is set but before positioning
        const tooltipHeight = tooltipElement.offsetHeight + 10;

        // Position tooltip above the element by subtracting tooltip height
        tooltipElement.style.top = `${top - tooltipHeight}px`;
    }

    function hideTooltip() {
        if (!tooltipTitleElement) {
            console.error("Tooltip title element not found!");
            return; // Prevent errors if the element wasn't selected correctly
        }
        tooltipElement.style.display = 'none';
    }

    // --- MODIFICATION END ---

    /**
     * Render flagged priorities section
     */
    function renderFlaggedPriorities() {
        const container = document.getElementById('flagged-priorities-list');
        if (!container) return; // Add check for container existence
        container.innerHTML = ''; // Clear previous items if any
        const { flagged_priorities } = carePlanData.care_plan || carePlanData;

        if (!flagged_priorities) return; // Check if data exists

        flagged_priorities.forEach(item => {
            const listItem = document.createElement('li');
            listItem.className = 'priority-item';
            listItem.innerHTML = `
            <span>${item.priority}</span>
            <span class="info-icon"></span>
        `;

            // Add click event to show reason
            const reasonText = item.reason || 'No reason provided'; // Ensure reasonText is defined
            listItem.addEventListener('mouseenter', (event) => {
                const rect = event.currentTarget.getBoundingClientRect();
                const top = rect.top;
                const left = rect.left;
                showTooltip('Reason', reasonText, top, left);
            });

            listItem.addEventListener('mouseleave', () => {
                hideTooltip();
            });

            container.appendChild(listItem);
        });
    }

    /**
     * Render provider-facing plan section with each subfield as its own collapsible accordion.
     */
    function renderProviderFacingPlan() {
        const container = document.getElementById('provider-plan-container');
        if (!container) return;
        container.innerHTML = ''; // Clear previous items
        const { provider_facing_plan } = carePlanData.care_plan || carePlanData;

        if (!provider_facing_plan) return;

        const subfieldOrder = [
            "vitals_and_screenings",
            "physical_exam",
            "medication_review",
            "labs_and_imaging",
            "counseling",
            "diagnosis_and_plan"
        ];

        subfieldOrder.forEach(key => {
            const data = provider_facing_plan[key];

            if (key === "labs_and_imaging") {
                const subfieldAccordion = document.createElement('div');
                subfieldAccordion.className = 'accordion-section subfield';

                const header = document.createElement('div');
                header.className = 'accordion-header';
                const formattedTitle = key.split('_')
                    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
                    .join(' ');
                header.innerHTML = `<h3>${formattedTitle}</h3>
                                <span class="toggle-icon"><i class="fas fa-chevron-down"></i></span>`;
                subfieldAccordion.appendChild(header);

                const content = document.createElement('div');
                content.className = 'accordion-content';

                const pairsContainer = document.createElement('div');
                pairsContainer.className = 'value-reason-pairs';

                for (const labkey in data) {
                    const labData = data[labkey];

                    for (let i = 0; i < labData.value.length; i++) {
                        const pair = document.createElement('div');
                        pair.className = `value-reason-pair ${labData.status[i] !== undefined ? labData.status[i] : ''}`;

                        // Basic value text is always shown
                        let innerHTML = `<span class="value-text">${labData.value[i].replace('Diagnosis:', '')}</span><span class="info-icon"></span>`;

                        // Only add status and provider if status exists and is defined for this index
                        // if (labData.status && labData.status[i] !== undefined) {
                        //     innerHTML += `<span class="status ${labData.status[i]}">`;

                        //     // Only add provider name if provider array exists and has non-empty value
                        //     if (labData.provider && labData.provider[i]) {
                        //         innerHTML += `<span class="provider-name">Missed by ${labData.provider[i]}</span>`;
                        //     }

                        //     innerHTML += `</span>`;
                        // }

                        pair.innerHTML = innerHTML;

                        // Only add click handler if we actually have a provider name element
                        // if (labData.provider && labData.provider[i]) {
                        //     const statusElement = pair.querySelector(".status");
                        //     if (statusElement) {
                        //         statusElement.addEventListener('mouseenter', (event) => {
                        //             event.stopPropagation();

                        //             const clickedProviderName = pair.querySelector(".provider-name");
                        //             if (clickedProviderName) {
                        //                 // Hide all other provider names first
                        //                 document.querySelectorAll(".provider-name").forEach((providerName) => {
                        //                     providerName.classList.remove("active");
                        //                 });

                        //                 // Show this provider name
                        //                 clickedProviderName.classList.add("active");
                        //             }
                        //         });

                        //         statusElement.addEventListener('mouseleave', (event) => {
                        //             const providerName = pair.querySelector(".provider-name");
                        //             if (providerName) {
                        //                 providerName.classList.remove("active");
                        //             }
                        //         });
                        //     }
                        // }

                        const reasonText = labData.reason[i] || 'No reason provided';
                        pair.addEventListener('mouseenter', (event) => {
                            const rect = event.currentTarget.getBoundingClientRect();
                            const top = rect.top;
                            const left = rect.left;
                            showTooltip('Reason', reasonText, top, left); // Pass 'Reason' as title
                        });

                        pair.addEventListener('mouseleave', () => {
                            hideTooltip();
                        });
                        pairsContainer.appendChild(pair);
                    }
                };

                content.appendChild(pairsContainer);
                subfieldAccordion.appendChild(content);
                container.appendChild(subfieldAccordion);
            }
            else if (key === "medication_review") {
                const subfieldAccordion = document.createElement('div');
                subfieldAccordion.className = 'accordion-section subfield';

                const header = document.createElement('div');
                header.className = 'accordion-header';
                header.innerHTML = `<h3>Medication Review</h3>
                              <span class="toggle-icon"><i class="fas fa-chevron-down"></i></span>`;
                subfieldAccordion.appendChild(header);

                const content = document.createElement('div');
                content.className = 'accordion-content';

                const pairsContainer = document.createElement('div');
                pairsContainer.className = 'value-reason-pairs';

                // Loop through each medication group
                data.forEach(medication => {
                    // Create diagnosis header
                    const diagnosisHeader = document.createElement('div');
                    diagnosisHeader.className = 'medication-diagnosis';
                    diagnosisHeader.textContent = medication.diagnosis;
                    pairsContainer.appendChild(diagnosisHeader);

                    // Loop through values for this medication
                    for (let i = 0; i < medication.value.length; i++) {
                        const pair = document.createElement('div');
                        pair.className = `value-reason-pair ${medication.status[i] !== undefined ? medication.status[i] : ''}`;

                        let innerHTML = `<span class="value-text">${medication.value[i]}</span><span class="info-icon"></span>`;

                        // if (medication.status && medication.status[i]) {
                        //     innerHTML += `<span class="status ${medication.status[i]}"></span>`;
                        // }

                        pair.innerHTML = innerHTML;

                        const reasonText = medication.reason[i] || 'No reason provided';
                        pair.addEventListener('mouseenter', (event) => {
                            const rect = event.currentTarget.getBoundingClientRect();
                            const top = rect.top;
                            const left = rect.left;
                            showTooltip('Reason', reasonText, top, left);
                        });

                        pair.addEventListener('mouseleave', () => {
                            hideTooltip();
                        });

                        pairsContainer.appendChild(pair);
                    }
                });

                content.appendChild(pairsContainer);
                subfieldAccordion.appendChild(content);
                container.appendChild(subfieldAccordion);
            }
            else {
                const subfieldAccordion = document.createElement('div');
                subfieldAccordion.className = 'accordion-section subfield';

                const header = document.createElement('div');
                header.className = 'accordion-header';
                const formattedTitle = key.split('_')
                    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
                    .join(' ');
                header.innerHTML = `<h3>${formattedTitle}</h3>
                                <span class="toggle-icon"><i class="fas fa-chevron-down"></i></span>`;
                subfieldAccordion.appendChild(header);

                const content = document.createElement('div');
                content.className = 'accordion-content';

                const pairsContainer = document.createElement('div');
                pairsContainer.className = 'value-reason-pairs';

                for (let i = 0; i < data.value.length; i++) {
                    const pair = document.createElement('div');
                    pair.className = `value-reason-pair ${data.status[i] !== undefined ? data.status[i] : ''}`;

                    // Basic value text is always shown
                    let innerHTML = `<span class="value-text">${data.value[i].replace('Diagnosis:', '')}</span><span class="info-icon"></span>`;

                    // Only add status and provider if status exists and is defined for this index
                    // if (data.status && data.status[i] !== undefined) {
                    //     innerHTML += `<span class="status ${data.status[i]}">`;

                    //     // Only add provider name if provider array exists and has non-empty value
                    //     if (data.provider && data.provider[i]) {
                    //         innerHTML += `<span class="provider-name">Missed by ${data.provider[i]}</span>`;
                    //     }

                    //     innerHTML += `</span>`;
                    // }

                    pair.innerHTML = innerHTML;

                    // Only add click handler if we actually have a provider name element
                    // if (data.provider && data.provider[i]) {
                    //     const statusElement = pair.querySelector(".status");
                    //     if (statusElement) {
                    //         statusElement.addEventListener('mouseenter', (event) => {
                    //             event.stopPropagation();

                    //             const clickedProviderName = pair.querySelector(".provider-name");
                    //             if (clickedProviderName) {
                    //                 // Hide all other provider names first
                    //                 document.querySelectorAll(".provider-name").forEach((providerName) => {
                    //                     providerName.classList.remove("active");
                    //                 });

                    //                 // Show this provider name
                    //                 clickedProviderName.classList.add("active");
                    //             }
                    //         });

                    //         statusElement.addEventListener('mouseleave', (event) => {
                    //             const providerName = pair.querySelector(".provider-name");
                    //             if (providerName) {
                    //                 providerName.classList.remove("active");
                    //             }
                    //         });
                    //     }
                    // }

                    const reasonText = data.reason && data.reason[i] ? data.reason[i] : 'No reason provided';
                    pair.addEventListener('mouseenter', (event) => {
                        const rect = event.currentTarget.getBoundingClientRect();
                        const top = rect.top;
                        const left = rect.left;
                        showTooltip('Reason', reasonText, top, left);
                    });

                    pair.addEventListener('mouseleave', () => {
                        hideTooltip();
                    });
                    pairsContainer.appendChild(pair);
                }

                content.appendChild(pairsContainer);
                subfieldAccordion.appendChild(content);
                container.appendChild(subfieldAccordion);
            }
        });
    }

    /**
     * Render patient-facing plan section
     */
    function renderPatientFacingPlan() {
        const container = document.getElementById('patient-plan-container');
        if (!container) return;
        container.innerHTML = ''; // Clear previous items
        const { patient_facing_plan } = carePlanData.care_plan || carePlanData;

        if (!patient_facing_plan) return;

        Object.entries(patient_facing_plan).forEach(([key, sectionData]) => {
            if (!Array.isArray(sectionData)) return; // Ensure sectionData is an array

            const sectionAccordion = document.createElement('div');
            sectionAccordion.className = 'accordion-section patient-subsection';

            const header = document.createElement('div');
            header.className = 'accordion-header';
            const formattedTitle = key.split('_')
                .map(word => word.charAt(0).toUpperCase() + word.slice(1))
                .join(' ');
            header.innerHTML = `<h3>${formattedTitle}</h3>
                            <span class="toggle-icon"><i class="fas fa-chevron-down"></i></span>`;
            sectionAccordion.appendChild(header);

            const content = document.createElement('div');
            content.className = 'accordion-content';

            const list = document.createElement('ul');
            list.className = 'patient-list';

            sectionData.forEach(itemGroup => {
                // Ensure itemGroup and its properties are valid arrays
                if (!itemGroup || !Array.isArray(itemGroup.item)) return;
                const reasons = Array.isArray(itemGroup.reason) ? itemGroup.reason : [];
                const educations = Array.isArray(itemGroup.education) ? itemGroup.education : [];

                itemGroup.item.forEach((itemText, index) => {
                    const listItem = document.createElement('li');
                    listItem.className = 'patient-item';

                    listItem.innerHTML = `
                        <span class="value-text">${itemText}</span>
                        <span class="info-icon"></span>
                    `;

                    // Determine the title and content for the tooltip
                    let tooltipTitle = 'Reason'; // Default title
                    let tooltipContent = '';

                    // Check specifically for education_instructions AND if education data exists for this item
                    if (key === "education_instructions" && educations[index]) {
                        tooltipTitle = 'Education'; // Set title to Education
                        tooltipContent = educations[index]; // Use the specific education text
                    } else {
                        // Otherwise, use the reason (if available) and keep title as 'Reason'
                        tooltipContent = reasons[index] || 'No details provided'; // Use reason or a generic fallback
                    }
                    // Add default if tooltipContent is still empty
                    if (!tooltipContent) {
                        tooltipContent = 'No details provided';
                    }

                    // Add click listener to the list item itself
                    listItem.addEventListener('mouseenter', (event) => {
                        event.stopPropagation();
                        const rect = event.currentTarget.getBoundingClientRect();
                        const top = rect.top;
                        const left = rect.left;
                        // Pass both the determined title and content
                        showTooltip(tooltipTitle, tooltipContent, top, left);
                    });

                    listItem.addEventListener('mouseleave', () => {
                        hideTooltip();
                    });

                    list.appendChild(listItem);
                });
            });

            content.appendChild(list);
            sectionAccordion.appendChild(content);

            // Add accordion toggle listener directly here
            if (!header.dataset.listenerAttached) {
                header.addEventListener('click', (event) => {
                    event.stopPropagation(); // Prevent event bubbling to any outer accordions
                    sectionAccordion.classList.toggle('active');
                });
                header.dataset.listenerAttached = 'true';
            }

            container.appendChild(sectionAccordion);
        });
    }


    /**
     * Render follow-up timeline section as a calendar grid
     */
    function renderFollowUpTimeline() {
        const container = document.getElementById('timeline-container');
        if (!container) return;
        container.innerHTML = ''; // Clear previous items
        const { follow_up_timeline } = carePlanData.care_plan || carePlanData;

        if (!Array.isArray(follow_up_timeline)) return; // Check if data is array

        follow_up_timeline.forEach(monthObj => {
            if (!monthObj) return; // Skip if month object is null/undefined
            const month = monthObj.month || 'Unnamed Month'; // Handle missing month name
            const monthCard = document.createElement('div');
            monthCard.className = 'month-card';
            monthCard.innerHTML = `<h3 class="month-header">${month} <button class="book-month-btn" disabled>Book <img src="./images/calender-icon.png" alt="Calendar Icon"></button></h3>`;

            // Add event listener to the book button
            monthCard.querySelector('.book-month-btn').addEventListener('click', function () {
                // Extract month and year from the month string
                const monthYearParts = month.split(' ');
                const monthName = monthYearParts[0];
                const year = monthYearParts[1] || new Date().getFullYear();

                // Show calendar popup
                showCalendarPopup(monthName, parseInt(year));

                trackEvent("clicked care timeline book button", "button click", `clicked care timeline book button`);
            });

            if (Array.isArray(monthObj.tasks) && monthObj.tasks.length > 0) {
                const taskList = document.createElement('ul');
                taskList.className = 'task-list';

                monthObj.tasks.forEach(taskGroup => {
                    // Ensure taskGroup and its properties are valid arrays
                    if (!taskGroup || !Array.isArray(taskGroup.task)) return;
                    const reasons = Array.isArray(taskGroup.reason) ? taskGroup.reason : [];

                    taskGroup.task.forEach((taskText, index) => {
                        const taskItem = document.createElement('li');
                        taskItem.className = 'task-item';
                        taskItem.innerHTML = `
                        <span class="value-text">${taskText}</span>
                        <span class="info-icon"></span>
                    `;

                        const reasonText = reasons[index] || 'No reason provided';
                        taskItem.addEventListener('mouseenter', () => {
                            const rect = event.currentTarget.getBoundingClientRect();
                            const top = rect.top;
                            const left = rect.left;
                            showTooltip('Reason', reasonText, top, left);
                        });

                        taskItem.addEventListener('mouseleave', () => {
                            hideTooltip();
                        });

                        taskList.appendChild(taskItem);
                    });
                });

                monthCard.appendChild(taskList);
            } else {
                const emptyMessage = document.createElement('p');
                emptyMessage.className = 'empty-month';
                emptyMessage.textContent = 'No follow-up tasks scheduled';
                monthCard.appendChild(emptyMessage);
            }

            container.appendChild(monthCard);
        });
    }

    function setupDropdowns() {
        // Setup days dropdown
        // const daysDropdownTrigger = document.querySelector('.num-days');
        // const daysDropdown = document.querySelector('.days-dropdown');
        // const dayItems = document.querySelectorAll('.day');

        // daysDropdownTrigger.addEventListener('click', () => {
        //     if(!daysDropdownTrigger.classList.contains('skeleton')){
        //         daysDropdown.classList.toggle('active');
        //     }
        // });

        // dayItems.forEach(item => {
        //     item.addEventListener('click', () => {
        //         daysDropdownTrigger.textContent = item.textContent;
        //         daysDropdown.classList.remove('active');
        //     });
        // });

        // Setup time dropdown
        // const timeDropdownTrigger = document.querySelector('.time-slot');
        // const timeDropdown = document.querySelector('.time-dropdown');
        // const timeItems = document.querySelectorAll('.time');

        // timeDropdownTrigger.addEventListener('click', () => {
        //     if(!timeDropdownTrigger.classList.contains('skeleton')){
        //         timeDropdown.classList.toggle('active');
        //     }
        // });

        // timeItems.forEach(item => {
        //     item.addEventListener('click', () => {
        //         timeDropdownTrigger.textContent = item.textContent.split(' - ')[0];
        //         timeDropdown.classList.remove('active');
        //     });
        // });

        // Close dropdowns when clicking outside
        // document.addEventListener('click', (event) => {
        //     if (!event.target.closest('.num-days-container') && daysDropdown.classList.contains('active')) {
        //         daysDropdown.classList.remove('active');
        //     }
        //     if (!event.target.closest('.time-slots') && timeDropdown.classList.contains('active')) {
        //         timeDropdown.classList.remove('active');
        //     }
        // });
    }

    // setupDropdowns();
});

// Initialize Google Identity Services
function initializeGoogleAuth() {
    // Initialize the Google Identity Services client
    google.accounts.id.initialize({
        client_id: '************-ql1sk1dhch2h4j4c0ku4tk2d3t228372.apps.googleusercontent.com',
        callback: handleCredentialResponse,
        auto_select: false
    });

    // Initialize the token client for OAuth
    tokenClient = google.accounts.oauth2.initTokenClient({
        client_id: '************-ql1sk1dhch2h4j4c0ku4tk2d3t228372.apps.googleusercontent.com',
        scope: 'email profile',
        callback: (tokenResponse) => {
            if (tokenResponse && tokenResponse.access_token) {
                // Get user info using the access token
                fetchUserInfo(tokenResponse.access_token);
            }
        }
    });

    // Add click handler to our custom button
    customSigninBtn.addEventListener('click', function () {
        tokenClient.requestAccessToken();
    });
}

// Handle the credential response (One Tap flow)
function handleCredentialResponse(response) {
    // Decode the credential to get user info
    const credential = parseJwt(response.credential);
    email = credential.email;

    // Store user name
    userName = credential.name || credential.given_name || email.split('@')[0];

    // Update the user name in the popup
    if (userNameElement) {
        userNameElement.textContent = userName;
    }

    // Check if user has naomedical.com email
    if (email.endsWith('@naomedical.com')) {
        // Save session
        saveUserSession(email, userName);

        // Show app content, hide login
        appContent.style.display = 'block';
        loginSection.style.display = 'none';

        trackEvent("sign in", "auth", `${userName} with ${email} signed in successfully`);
    } else {
        showPopup('Authentication failed. Please sign in with an authorized account.', true);
    }
}

// Fetch user info using access token (OAuth flow)
function fetchUserInfo(accessToken) {
    fetch('https://www.googleapis.com/oauth2/v3/userinfo', {
        headers: {
            'Authorization': `Bearer ${accessToken}`
        }
    })
        .then(response => response.json())
        .then(data => {
            const email = data.email;

            // Store user name
            userName = data.name || data.given_name || email.split('@')[0];

            // Update the user name in the popup
            if (userNameElement) {
                userNameElement.textContent = userName;
            }

            // Check if user has naomedical.com email
            if (email.endsWith('@naomedical.com')) {
                // Save session
                saveUserSession(email, userName);

                // Show app content, hide login
                appContent.style.display = 'block';
                loginSection.style.display = 'none';

                trackEvent("sign in", "auth", `${userName} with ${email} signed in successfully`);
            } else {
                showPopup('Authentication failed. Please sign in with an authorized account.', true);
            }
        })
        .catch(error => {
            console.error('Error fetching user info:', error);
        });
}

// Helper function to parse JWT token
function parseJwt(token) {
    const base64Url = token.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(atob(base64).split('').map(function (c) {
        return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
    }).join(''));
    return JSON.parse(jsonPayload);
}

// Check if user is already logged in with expiration
function checkExistingSession() {
    const userSession = JSON.parse(localStorage.getItem('userSession'));

    if (userSession && userSession.isLoggedIn) {
        // Check if session has expired (e.g., after 24 hours)
        const now = new Date().getTime();
        const sessionAge = now - userSession.timestamp;
        const sessionLimit = 24 * 60 * 60 * 1000; // 24 hours

        if (sessionAge > sessionLimit) {
            // Session expired, clear it
            clearUserSession();
            return;
        }

        // Restore user data
        userName = userSession.userName || '';

        // Update UI
        if (userNameElement) {
            userNameElement.textContent = userName;
        }

        // Show app content, hide login
        appContent.style.display = 'block';
        loginSection.style.display = 'none';
    }
}

// Save user session to localStorage
function saveUserSession(email, name) {
    const userSession = {
        isLoggedIn: true,
        email: email,
        userName: name,
        timestamp: new Date().getTime()
    };

    localStorage.setItem('userSession', JSON.stringify(userSession));
}

// Clear user session from localStorage
function clearUserSession() {
    localStorage.removeItem('userSession');
}

// Handle sign out
document.getElementById('signout-btn').addEventListener('click', function () {
    // Sign out and revoke token
    google.accounts.id.disableAutoSelect();

    // Clear session
    clearUserSession();

    // Reset UI
    appContent.style.display = 'none';
    loginSection.style.display = 'block';

    // Close popup if open
    profilePopup.classList.remove('active');

    trackEvent("Sign out", "auth", `${userName} signed out`);
});

// Refresh the session timestamp
function refreshSession() {
    const userSession = JSON.parse(localStorage.getItem('userSession'));

    if (userSession && userSession.isLoggedIn) {
        userSession.timestamp = new Date().getTime();
        localStorage.setItem('userSession', JSON.stringify(userSession));
    }
}

// Call this periodically or on user interactions
document.addEventListener('click', function () {
    if (appContent.style.display === 'block') {
        refreshSession();
    }
});

// Remove the old Google Sign-In API script
// document.write('<script src="https://apis.google.com/js/platform.js?onload=loadGoogleAuth" async defer></script>');

/**
 * Shows a calendar popup for booking appointments
 * @param {string} monthName - The name of the month to display
 * @param {number} year - The year to display
 */
function showCalendarPopup(monthName, year) {
    // Get the existing popup container
    const calendarPopup = document.getElementById('calendar-popup');
    if (!calendarPopup) return;

    // Get month number from name
    const monthNames = ['January', 'February', 'March', 'April', 'May', 'June',
        'July', 'August', 'September', 'October', 'November', 'December'];
    const monthIndex = monthNames.findIndex(m => monthName.includes(m));
    const month = monthIndex !== -1 ? monthIndex : new Date().getMonth();

    // Update the month and year in the header
    const headerTitle = calendarPopup.querySelector('.calendar-header h3');
    if (headerTitle) {
        headerTitle.textContent = `${monthNames[month]} ${year}`;
    }

    // Update the calendar days
    const calendarDaysWrapper = calendarPopup.querySelector('.calendar-days-wrapper');
    if (calendarDaysWrapper) {
        // Clear the wrapper before adding new days
        calendarDaysWrapper.innerHTML = '';
        // Add the generated days
        calendarDaysWrapper.innerHTML = generateCalendarDays(month, year);
    }

    // Add reason input field if it doesn't exist
    let reasonInput = calendarPopup.querySelector('.reason-input');
    if (!reasonInput) {
        reasonInput = document.createElement('div');
        reasonInput.className = 'reason-input';
        reasonInput.innerHTML = `
            <input type="text" id="appointment-reason" placeholder="Enter reason for visit">
        `;
        // Insert before the confirm button
        const confirmBtn = calendarPopup.querySelector('.confirm-btn');
        if (confirmBtn) {
            confirmBtn.parentNode.insertBefore(reasonInput, confirmBtn);
        }
    }

    // Show the popup
    calendarPopup.style.display = 'flex';

    // Add event listeners
    setupCalendarEventListeners(calendarPopup, month, parseInt(year));

    // Fetch time slots for all dates in the month in parallel
    fetchTimeSlotsForMonth(month, year);
}

/**
 * Generates the HTML for calendar days
 */
function generateCalendarDays(month, year) {
    const date = new Date(year, month, 1);
    const daysInMonth = new Date(year, month + 1, 0).getDate();
    const firstDayIndex = date.getDay();

    let daysHTML = '';

    // Get current date for comparison
    const today = new Date();
    const currentYear = today.getFullYear();
    const currentMonth = today.getMonth();
    const currentDay = today.getDate();

    // Add empty cells for days before the 1st of the month
    for (let i = 0; i < firstDayIndex; i++) {
        const prevMonth = month === 0 ? 11 : month - 1;
        const prevYear = month === 0 ? year - 1 : year;
        const daysInPrevMonth = new Date(prevYear, prevMonth + 1, 0).getDate();
        const day = daysInPrevMonth - firstDayIndex + i + 1;
        daysHTML += `<div class="calendar-day inactive">${day}</div>`;
    }

    // Add days of the current month
    for (let i = 1; i <= daysInMonth; i++) {
        // Check if the date is in the past
        const isPastDate = (year < currentYear) ||
            (year === currentYear && month < currentMonth) ||
            (year === currentYear && month === currentMonth && i < currentDay);

        // Set default selected date to current day
        const isDefaultDate = i === currentDay && !isPastDate;

        const classes = ['calendar-day'];
        if (isPastDate) {
            classes.push('disabled');
            classes.push('inactive');
        }
        if (isDefaultDate) {
            classes.push('selected');
        }

        daysHTML += `<div class="${classes.join(' ')}">${i}</div>`;
    }

    const selectedDate = new Date(year, month, currentDay);
    // Convert to EST timezone
    const estDate = new Date(selectedDate.toLocaleString('en-US', { timeZone: 'America/New_York' }));
    const formattedDate = estDate.toISOString().split('T')[0];

    // Fetch slots for the first day of the month
    fetchCalendarSlots(selectedLocationId, formattedDate, selectedProviderId);

    // Add days for the next month to fill the grid
    const totalCells = Math.ceil((firstDayIndex + daysInMonth) / 7) * 7;
    const remainingCells = totalCells - (firstDayIndex + daysInMonth);

    for (let i = 1; i <= remainingCells; i++) {
        daysHTML += `<div class="calendar-day inactive">${i}</div>`;
    }

    return daysHTML;
}

/**
 * Sets up event listeners for the calendar popup
 */
function setupCalendarEventListeners(popup, currentMonth, currentYear) {
    // Close popup when clicking outside
    popup.addEventListener('click', function (e) {
        if (e.target === popup) {
            popup.style.display = 'none';
        }
    });

    // Close button
    popup.querySelector('.close-calendar').addEventListener('click', function () {
        popup.style.display = 'none';
    });

    // Previous month button
    popup.querySelector('.prev-month').addEventListener('click', function () {
        let newMonth = currentMonth - 1;
        let newYear = currentYear;

        if (newMonth < 0) {
            newMonth = 11;
            newYear--;
        }

        showCalendarPopup(getMonthName(newMonth), newYear);
    });

    // Next month button
    popup.querySelector('.next-month').addEventListener('click', function () {
        let newMonth = currentMonth + 1;
        let newYear = currentYear;

        if (newMonth > 11) {
            newMonth = 0;
            newYear++;
        }

        showCalendarPopup(getMonthName(newMonth), newYear);
    });

    // Day selection
    popup.querySelectorAll('.calendar-day:not(.inactive):not(.disabled)').forEach(day => {
        day.addEventListener('click', function () {
            // Remove selected class from all days
            popup.querySelectorAll('.calendar-day').forEach(d => d.classList.remove('selected'));
            // Add selected class to clicked day
            this.classList.add('selected');

            // Get selected date
            const selectedDay = parseInt(this.textContent);
            const selectedDate = new Date(currentYear, currentMonth, selectedDay);
            // Convert to EST timezone
            const estDate = new Date(selectedDate.toLocaleString('en-US', { timeZone: 'America/New_York' }));
            const formattedDate = estDate.toISOString().split('T')[0];

            // Show skeleton loading for time slots
            const timeDropdown = popup.querySelector('.time-dropdown');
            timeDropdown.innerHTML = '<div class="time-slot skeleton">Loading...</div>';

            // Fetch slots for the selected date
            fetchCalendarSlots(selectedLocationId, formattedDate, selectedProviderId);
        });
    });

    // Confirm button - Remove existing listeners and add new one
    const confirmBtn = popup.querySelector('.confirm-btn');
    const newConfirmBtn = confirmBtn.cloneNode(true);
    confirmBtn.parentNode.replaceChild(newConfirmBtn, confirmBtn);

    newConfirmBtn.addEventListener('click', async function () {
        const selectedDay = popup.querySelector('.calendar-day.selected')?.textContent;
        const selectedTime = popup.querySelector('.time.selected')?.textContent;
        const reasonInput = popup.querySelector('#appointment-reason');
        const reason = reasonInput ? reasonInput.value : '';

        if (selectedDay && selectedTime) {
            try {
                // Disable confirm button and show loading state
                const confirmBtn = this;
                const originalBtnText = confirmBtn.textContent;
                confirmBtn.disabled = true;
                confirmBtn.textContent = 'Booking...';
                confirmBtn.classList.add('loading');

                // Format the selected date from calendar and convert to EST
                const selectedDate = new Date(currentYear, currentMonth, parseInt(selectedDay));
                const estDate = new Date(selectedDate.toLocaleString('en-US', { timeZone: 'America/New_York' }));
                const formattedDate = estDate.toISOString().split('T')[0];

                // Parse the time slot text to get start and end times
                const [startTimeText, endTimeText] = selectedTime.split(' to ');

                // Convert time to 24-hour format
                const startTime = convertTo24Hour(startTimeText);
                const endTime = convertTo24Hour(endTimeText);

                // Parse hours and minutes for startTime object
                const [hours, minutes] = startTime.split(':').map(Number);

                // Create appointment payload
                const appointmentPayload = {
                    "locationId": String(selectedLocationId),
                    "visitType": "pcp",
                    "providerId": selectedProviderId,
                    "startDate": formattedDate,
                    "createdBy": selectedProviderName,
                    "source": "Careplan",
                    "start_time": startTime,
                    "end_time": endTime,
                    "visitReason": reason || "follow-up",
                    "patient_name": `${firstName} ${lastName}`,
                    "startTime": {
                        "startTime": {
                            "hours": hours,
                            "minutes": minutes
                        }
                    },
                    "patientId": selectedPatientId
                };

                // Make API call to create appointment
                const response = await fetch('https://nexhealth-custom-server-*************.us-central1.run.app/api/locations/create-appointment-3', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(appointmentPayload)
                });


                const appointmentData = await response.json();

                // Check for specific error response
                if (appointmentData.status === 0 && appointmentData.error === "Failed to create appointment") {
                    // Get the specific error message from the details.error array if available
                    const errorMessage = appointmentData.details?.error?.[0] || 'Failed to book appointment. Please try again.';
                    showPopup(errorMessage, true);
                } else {
                    showPopup('Appointment booked successfully!');

                    // Refresh time slots for the current date
                    const timeDropdown = popup.querySelector('.time-dropdown');
                    const timeSlotTrigger = popup.querySelector('.time-slot');

                    if (timeDropdown) {
                        timeDropdown.innerHTML = '<div class="time-slot skeleton">Loading...</div>';
                    }
                    if (timeSlotTrigger) {
                        timeSlotTrigger.textContent = 'Loading...';
                        timeSlotTrigger.classList.add('skeleton');
                    }

                    // Fetch updated slots
                    await fetchCalendarSlots(selectedLocationId, formattedDate, selectedProviderId);
                }
            } catch (error) {
                console.error('Error creating appointment:', error);

                // Try to parse the error response if it exists
                let errorMessage = 'Failed to book appointment. Please try again.';

                if (error.response) {
                    try {
                        const errorData = await error.response.json();
                        if (errorData.status === 0 && errorData.error === "Failed to create appointment") {
                            errorMessage = errorData.details?.error?.[0] || errorMessage;
                        }
                    } catch (parseError) {
                        console.error('Error parsing error response:', parseError);
                    }
                }

                showPopup(errorMessage, true);
            } finally {
                // Reset confirm button state
                const confirmBtn = this;
                confirmBtn.disabled = false;
                confirmBtn.textContent = 'Confirm';
                confirmBtn.classList.remove('loading');
            }
        } else {
            showPopup('Please select both a day and time slot', true);
        }
    });
}

/**
 * Helper function to get month name from index
 */
function getMonthName(monthIndex) {
    const monthNames = ['January', 'February', 'March', 'April', 'May', 'June',
        'July', 'August', 'September', 'October', 'November', 'December'];
    return monthNames[monthIndex];
}

// Add this global variable at the top with other globals
let patientAppointments = [];

// Add a global variable to store provider details
let providerDetails = null;

// Add these global variables at the top
let isAppointmentsLoaded = false;
let isProviderDetailsLoaded = false;

// Add this new function to analyze and select best time slots
function getSuggestedTimeSlots(slots) {
    if (!slots || !slots.options || slots.options.length === 0) {
        return [];
    }

    // Sort slots by time
    const sortedSlots = [...slots.options].sort((a, b) => {
        const timeA = a.text.split(' to ')[0];
        const timeB = b.text.split(' to ')[0];
        return timeA.localeCompare(timeB);
    });

    // If only 1 slot, return empty array (no suggestions)
    if (sortedSlots.length === 1) {
        return [];
    }

    // If 2 slots, return 1 suggestion
    if (sortedSlots.length === 2) {
        return [sortedSlots[0]];
    }

    // If 3 slots, return 2 suggestions
    if (sortedSlots.length === 3) {
        return [sortedSlots[0], sortedSlots[1]];
    }

    // For 4 or more slots, implement spacing-based selection
    const timeToMinutes = (timeStr) => {
        const [time, period] = timeStr.split(' ');
        let [hours, minutes] = time.split(':').map(Number);
        if (period === 'PM' && hours !== 12) hours += 12;
        if (period === 'AM' && hours === 12) hours = 0;
        return hours * 60 + minutes;
    };

    // Calculate spacing scores for each slot
    const slotsWithScores = sortedSlots.map((slot, index) => {
        const slotTime = timeToMinutes(slot.text.split(' to ')[0]);
        let totalSpacing = 0;
        let spacingCount = 0;

        // Calculate average spacing from all other slots
        sortedSlots.forEach((otherSlot, otherIndex) => {
            if (index !== otherIndex) {
                const otherTime = timeToMinutes(otherSlot.text.split(' to ')[0]);
                const spacing = Math.abs(slotTime - otherTime);
                totalSpacing += spacing;
                spacingCount++;
            }
        });

        const averageSpacing = totalSpacing / spacingCount;

        // Bonus for morning slots (before 12 PM)
        const isMorning = slotTime < 12 * 60;
        const morningBonus = isMorning ? 30 : 0;

        return {
            slot,
            score: averageSpacing + morningBonus
        };
    });

    // Sort slots by score (higher is better)
    slotsWithScores.sort((a, b) => b.score - a.score);

    // Select slots ensuring good distribution
    const selectedSlots = [];
    const minSpacing = 60; // Minimum 1 hour between slots

    for (const { slot } of slotsWithScores) {
        if (selectedSlots.length >= 4) break;

        const slotTime = timeToMinutes(slot.text.split(' to ')[0]);

        // Check if this slot is far enough from already selected slots
        const isWellSpaced = selectedSlots.every(selectedSlot => {
            const selectedTime = timeToMinutes(selectedSlot.text.split(' to ')[0]);
            return Math.abs(slotTime - selectedTime) >= minSpacing;
        });

        if (isWellSpaced) {
            selectedSlots.push(slot);
        }
    }

    // If we couldn't find 4 well-spaced slots, add more slots
    if (selectedSlots.length < 4) {
        for (const { slot } of slotsWithScores) {
            if (selectedSlots.length >= 4) break;
            if (!selectedSlots.includes(slot)) {
                selectedSlots.push(slot);
            }
        }
    }

    return selectedSlots;
}

// Modify the fetchCalendarSlots function
async function fetchCalendarSlots(locationId, startDate, providerId) {
    try {
        // Show skeleton loading state
        const timeSlotsContainer = document.querySelector('.time-slots-container .time-dropdown');
        const timeSlotTrigger = document.querySelector('.time-slots-container .time-slot');

        if (timeSlotsContainer) {
            timeSlotsContainer.innerHTML = '<div class="time-slot skeleton">Loading...</div>';
        }
        if (timeSlotTrigger) {
            timeSlotTrigger.textContent = 'Loading...';
            timeSlotTrigger.classList.add('skeleton');
        }

        const response = await fetch('https://nexhealth-custom-server-*************.us-central1.run.app/api/custom/slots', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                "locationId": locationId,
                "startDate": startDate,
                "providerId": providerId
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }

        const slotsData = await response.json();

        // Remove skeleton loading state
        if (timeSlotTrigger) {
            timeSlotTrigger.classList.remove('skeleton');
        }

        if (timeSlotsContainer) {
            timeSlotsContainer.innerHTML = ''; // Clear existing items

            if (slotsData.no_date) {
                // Show no slots available message
                const noSlotsMessage = document.createElement('div');
                noSlotsMessage.className = 'no-slots-message';
                noSlotsMessage.textContent = slotsData.message || 'No Slots available for this provider.';
                timeSlotsContainer.appendChild(noSlotsMessage);

                // Update trigger to show message
                if (timeSlotTrigger) {
                    timeSlotTrigger.textContent = 'No slots available';
                }
            } else if (slotsData.options) {
                // Get suggested time slots
                const suggestedSlots = getSuggestedTimeSlots(slotsData);

                // Create suggested slots section if we have suggestions
                if (suggestedSlots.length > 0) {
                    const suggestedSection = document.createElement('div');
                    suggestedSection.className = 'suggested-slots-section';

                    const separatorSuggest = document.createElement('div');
                    separatorSuggest.className = 'slots-separator';
                    separatorSuggest.innerHTML = '<span>Suggested Times</span>';
                    suggestedSection.appendChild(separatorSuggest);

                    const suggestedList = document.createElement('div');
                    suggestedList.className = 'suggested-slots-list';

                    suggestedSlots.forEach((slot, index) => {
                        const slotElement = document.createElement('span');
                        // Make the first suggested slot selected by default
                        slotElement.className = `time suggested-slot ${index === 0 ? 'selected' : ''}`;
                        slotElement.textContent = slot.text;
                        slotElement.dataset.slotId = slot.id;
                        suggestedList.appendChild(slotElement);

                        // If this is the first suggested slot, update the trigger
                        if (index === 0 && timeSlotTrigger) {
                            timeSlotTrigger.textContent = slot.text.split(' to ')[0];
                        }
                    });

                    suggestedSection.appendChild(suggestedList);
                    timeSlotsContainer.appendChild(suggestedSection);

                    // Add separator
                    const separator = document.createElement('div');
                    separator.className = 'slots-separator';
                    separator.innerHTML = '<span>All Available Times</span>';
                    timeSlotsContainer.appendChild(separator);
                }

                // Add all available slots
                slotsData.options.forEach((slot, index) => {
                    const slotElement = document.createElement('span');
                    // Only add selected class if there are no suggested slots
                    slotElement.className = `time dropdown-item ${(suggestedSlots.length === 0 && index === 0) ? 'selected' : ''}`;
                    slotElement.textContent = slot.text;
                    slotElement.dataset.slotId = slot.id;
                    timeSlotsContainer.appendChild(slotElement);

                    // If there are no suggested slots, set the first available slot as default
                    if (suggestedSlots.length === 0 && index === 0 && timeSlotTrigger) {
                        timeSlotTrigger.textContent = slot.text.split(' to ')[0];
                    }
                });

                // Add click event listeners to time slots
                timeSlotsContainer.querySelectorAll('.time').forEach(item => {
                    item.addEventListener('click', () => {
                        timeSlotsContainer.querySelectorAll('.time').forEach(slot => slot.classList.remove('selected'));
                        item.classList.add('selected');
                        if (timeSlotTrigger) {
                            timeSlotTrigger.textContent = item.textContent.split(' to ')[0];
                        }
                    });
                });
            }
        }

        return slotsData;
    } catch (error) {
        console.error('Error fetching calendar slots:', error);

        // Show error message in the calendar popup
        const timeSlotsContainer = document.querySelector('.time-slots-container .time-dropdown');
        const timeSlotTrigger = document.querySelector('.time-slots-container .time-slot');

        if (timeSlotsContainer) {
            timeSlotsContainer.innerHTML = '<div class="error-message">Error loading slots</div>';
        }

        if (timeSlotTrigger) {
            timeSlotTrigger.textContent = 'Error loading slots';
            timeSlotTrigger.classList.remove('skeleton');
        }

        return null;
    }
}

// Function for followup wrapper time slots
// async function fetchFollowupSlots(locationId, startDate, providerId) {
//     try {
//         const response = await fetch('https://nexhealth-custom-server-*************.us-central1.run.app/api/custom/slots', {
//             method: 'POST',
//             headers: {
//                 'Content-Type': 'application/json',
//             },
//             body: JSON.stringify({
//                 "locationId": locationId,
//                 "startDate": startDate,
//                 "providerId": providerId
//             })
//         });

//         if (!response.ok) {
//             throw new Error(`HTTP error! Status: ${response.status}`);
//         }

//         const slotsData = await response.json();
//         console.log('Followup Slots Data:', slotsData);

//         // Update time slots in followup wrapper
//         const timeSlotsContainer = document.querySelector('.followup-wrapper .time-dropdown');
//         const timeSlotTrigger = document.querySelector('.followup-wrapper .time-slot');

//         if (timeSlotsContainer) {
//             timeSlotsContainer.innerHTML = ''; // Clear existing items

//             if (slotsData.no_date) {
//                 // Show no slots available message
//                 const noSlotsMessage = document.createElement('div');
//                 noSlotsMessage.className = 'no-slots-message';
//                 noSlotsMessage.textContent = slotsData.message || 'No Slots available for this provider.';
//                 timeSlotsContainer.appendChild(noSlotsMessage);

//                 // Update trigger to show message
//                 if (timeSlotTrigger) {
//                     timeSlotTrigger.textContent = 'No slots available';
//                 }
//             } else if (slotsData.options) {
//                 timeSlotTrigger.classList.remove("skeleton")
//                 slotsData.options.forEach((slot, index) => {
//                     const slotElement = document.createElement('span');
//                     slotElement.className = `time dropdown-item ${index === 0 ? 'selected' : ''}`;
//                     slotElement.textContent = slot.text;
//                     slotElement.dataset.slotId = slot.id;
//                     timeSlotsContainer.appendChild(slotElement);
//                 });

//                 // Set default selected value
//                 if (slotsData.options.length > 0) {
//                     const firstSlot = slotsData.options[0].text.split(' to ')[0];
//                     if (timeSlotTrigger) {
//                         timeSlotTrigger.textContent = firstSlot;
//                     }
//                 }

//                 // Add click event listeners to time slots
//                 timeSlotsContainer.querySelectorAll('.time').forEach(item => {
//                     item.addEventListener('click', () => {
//                         timeSlotsContainer.querySelectorAll('.time').forEach(slot => slot.classList.remove('selected'));
//                         item.classList.add('selected');
//                         if (timeSlotTrigger) {
//                             timeSlotTrigger.textContent = item.textContent.split(' to ')[0];
//                         }
//                     });
//                 });
//             }
//         }

//         return slotsData;
//     } catch (error) {
//         console.error('Error fetching followup slots:', error);

//         // Show error message in the followup wrapper
//         const timeSlotsContainer = document.querySelector('.followup-wrapper .time-dropdown');
//         const timeSlotTrigger = document.querySelector('.followup-wrapper .time-slot');

//         if (timeSlotsContainer) {
//             timeSlotsContainer.innerHTML = '<div class="error-message">Error loading slots</div>';
//         }

//         if (timeSlotTrigger) {
//             timeSlotTrigger.textContent = 'Error loading slots';
//         }

//         return null;
//     }
// }

// Add this function to add skeleton loading to book buttons
function addSkeletonLoadingToBookButtons() {
    // Add skeleton class to all book buttons
    document.querySelectorAll('.book-btn, .book-month-btn').forEach(btn => {
        btn.classList.add('skeleton');
        btn.disabled = true;
    });
}

// Add this function to remove skeleton loading from book buttons
function removeSkeletonLoadingFromBookButtons() {
    // Remove skeleton class from all book buttons
    document.querySelectorAll('.book-btn, .book-month-btn').forEach(btn => {
        btn.classList.remove('skeleton');
        btn.disabled = false;
    });
}

// Update the fetchPatientAppointments function to use skeleton loading
async function fetchPatientAppointments(accountNumber) {
    try {
        // Add skeleton loading to book buttons
        addSkeletonLoadingToBookButtons();

        // Show skeleton loading state
        const selectedProviderElement = document.querySelector('.selected-provider');
        selectedProviderElement.classList.add('skeleton');
        selectedProviderElement.textContent = '';

        const response = await fetch('https://nexhealth-custom-server-*************.us-central1.run.app/api/follow/many-appts', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                "eCWId": accountNumber
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }

        patientAppointments = await response.json();

        // Update the provider dropdown with the fetched data
        updateProviderDropdown(patientAppointments);

        isAppointmentsLoaded = true;
        checkAndEnableBookButtons();

        // Remove skeleton loading from book buttons
        removeSkeletonLoadingFromBookButtons();

        return patientAppointments;
    } catch (error) {
        console.error('Error fetching patient appointments:', error);

        // Reset skeleton on error
        const selectedProviderElement = document.querySelector('.selected-provider');
        selectedProviderElement.classList.remove('skeleton');
        selectedProviderElement.textContent = 'Error loading providers';

        // Remove skeleton loading from book buttons
        removeSkeletonLoadingFromBookButtons();

        return [];
    }
}

// Function to update the provider dropdown with data from the API
function updateProviderDropdown(appointments) {
    const providerDropdown = document.querySelector('.provider-dropdown');
    const selectedProviderElement = document.querySelector('.selected-provider');

    // Clear existing dropdown items
    providerDropdown.innerHTML = '';

    // Add search input field
    const searchContainer = document.createElement('div');
    searchContainer.className = 'provider-search-container';
    searchContainer.innerHTML = `
        <input type="text" class="provider-search" placeholder="Search providers...">
    `;
    providerDropdown.appendChild(searchContainer);

    // Add search functionality
    const searchInput = searchContainer.querySelector('.provider-search');
    searchInput.addEventListener('input', function (e) {
        const searchTerm = e.target.value.toLowerCase();
        const providerItems = providerDropdown.querySelectorAll('.provider');

        providerItems.forEach(item => {
            const providerName = item.textContent.toLowerCase();
            if (providerName.includes(searchTerm)) {
                item.style.display = '';
            } else {
                item.style.display = 'none';
            }
        });
    });

    // Remove skeleton class and loading text
    selectedProviderElement.classList.remove('skeleton');
    selectedProviderElement.textContent = '';

    // Hide all book buttons if appointmentsByLocation is empty
    if (!appointments.appointmentsByLocation || appointments.appointmentsByLocation.length === 0) {
        document.querySelectorAll('.book-btn').forEach(btn => {
            btn.style.display = 'none';
        });
        document.querySelectorAll('.book-month-btn').forEach(btn => {
            btn.style.display = 'none';
        });
        selectedProviderElement.textContent = 'No providers';
        selectedProviderId = null;
        selectedProviderName = null;
        selectedLocationId = null;
        selectedPatientId = null;
        return;
    }

    // Show all book buttons if we have appointments
    document.querySelectorAll('.book-btn').forEach(btn => {
        btn.style.display = 'block';
    });
    document.querySelectorAll('.book-month-btn').forEach(btn => {
        btn.style.display = 'block';
    });

    // Keep track of added providers to avoid duplicates
    const addedProviders = new Set();

    // Add providers from appointments to dropdown
    appointments.appointmentsByLocation.forEach(item => {
        const providerName = item.appointment.provider_name;
        const providerId = item.appointment.provider_id;
        const locationId = item.appointment.location_id;
        const patientId = item.appointment.patient_id;
        const locationName = item.locationName;

        // Create a unique key combining provider name and ID
        const providerKey = `${providerName}-${providerId}`;

        // Skip if this provider has already been added
        if (addedProviders.has(providerKey)) {
            return;
        }

        // Add to our tracking set
        addedProviders.add(providerKey);

        const providerElement = document.createElement('span');
        providerElement.className = 'provider dropdown-item';
        providerElement.textContent = providerName;

        // Store provider data as attributes
        providerElement.dataset.providerId = providerId;
        providerElement.dataset.providerName = providerName;
        providerElement.dataset.locationId = locationId;
        providerElement.dataset.patientId = patientId;
        providerElement.dataset.locationName = locationName;

        // Add click event to update selected provider
        providerElement.addEventListener('click', function () {
            // Update the displayed provider name
            selectedProviderElement.textContent = providerName;

            // Update global variables with selected provider details
            selectedProviderId = providerId;
            selectedProviderName = providerName;
            selectedLocationId = locationId;
            selectedPatientId = patientId;
            selectedLocationName = locationName;

            // Find and set the initial location name from locMap
            const locationInfo = locMap.find(loc => loc.locationName === locationName);
            if (locationInfo) {
                const locNameText = document.querySelector('.loc-name-text');
                if (locNameText) {
                    locNameText.textContent = locationInfo.text;
                }
            }

            // Fetch provider details with the selected provider
            fetchProviderDetails(providerName, patientId, String(locationId), providerId, locationName);

            // If calendar popup is open, fetch new slots for the selected date
            const calendarPopup = document.getElementById('calendar-popup');
            if (calendarPopup && calendarPopup.style.display === 'flex') {
                const selectedDay = calendarPopup.querySelector('.calendar-day.selected')?.textContent;
                if (selectedDay) {
                    // Show skeleton loading for time slots
                    const timeDropdown = calendarPopup.querySelector('.time-dropdown');
                    const timeSlotTrigger = calendarPopup.querySelector('.time-slot');
                    if (timeDropdown) {
                        timeDropdown.innerHTML = '<div class="time-slot skeleton">Loading...</div>';
                    }
                    if (timeSlotTrigger) {
                        timeSlotTrigger.textContent = 'Loading...';
                        timeSlotTrigger.classList.add('skeleton');
                    }

                    // Get current month and year from header
                    const headerTitle = calendarPopup.querySelector('.calendar-header h3').textContent;
                    const [monthName, year] = headerTitle.split(' ');
                    const monthIndex = getMonthIndex(monthName);

                    // Format date for API
                    const selectedDate = new Date(parseInt(year), monthIndex, parseInt(selectedDay));
                    const formattedDate = selectedDate.toISOString().split('T')[0];

                    // Fetch new slots for the selected date
                    fetchCalendarSlots(locationId, formattedDate, providerId);

                    fetchTimeSlotsForMonth(monthIndex, parseInt(year));
                }
            }

            // Hide dropdown after selection
            providerDropdown.classList.remove('active');
        });

        providerDropdown.appendChild(providerElement);
    });

    // If we have providers, set the first one as default
    if (appointments.appointmentsByLocation.length > 0) {
        const firstProvider = appointments.appointmentsByLocation[0].appointment;
        selectedProviderElement.textContent = firstProvider.provider_name;
        selectedProviderId = firstProvider.provider_id;
        selectedProviderName = firstProvider.provider_name;
        selectedLocationId = firstProvider.location_id;
        selectedPatientId = firstProvider.patient_id;
        selectedLocationName = appointments.appointmentsByLocation[0].locationName;

        // Find and set the initial location name from locMap
        const locationInfo = locMap.find(loc => loc.locationName === selectedLocationName);
        if (locationInfo) {
            const locNameText = document.querySelector('.loc-name-text');
            if (locNameText) {
                locNameText.textContent = locationInfo.text;
            }
        }

        // Fetch provider details for the default provider
        fetchProviderDetails(
            firstProvider.provider_name,
            firstProvider.patient_id,
            String(firstProvider.location_id),
            firstProvider.provider_id,
            selectedLocationName
        );

    } else {
        // If no providers found, show a message
        selectedProviderElement.textContent = 'No providers';
        selectedProviderId = null;
        selectedProviderName = null;
        selectedLocationId = null;
        selectedPatientId = null;
    }
}

async function fetchProviderDetails(providerName, patientId, locationId, providerId, locationName) {
    try {
        // Show skeleton loading state for days dropdown only
        // const daysDropdown = document.querySelector('.days-dropdown');
        // const numDaysTrigger = document.querySelector('.num-days');

        // Add skeleton class to days trigger
        // numDaysTrigger.classList.add('skeleton');
        // numDaysTrigger.textContent = '';

        // Clear and add skeleton items to days dropdown
        // if (daysDropdown) {
        //     daysDropdown.innerHTML = '';
        //     for (let i = 0; i < 4; i++) {
        //         const skeletonItem = document.createElement('span');
        //         skeletonItem.className = 'day dropdown-item skeleton';
        //         skeletonItem.textContent = '';
        //         daysDropdown.appendChild(skeletonItem);
        //     }
        // }

        const response = await fetch('https://nexhealth-custom-server-*************.us-central1.run.app/api/internal/fetch-the-provider', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                "providerName": providerName,
                "patientLocationKey": locationName,
                "patientNexId": patientId,
                "locationId": locationId,
                "providerId": providerId,
                "customDate": "",
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }

        providerDetails = await response.json();

        // Remove skeleton class and update content for days dropdown
        // numDaysTrigger.classList.remove('skeleton');

        // // Update the days dropdown with availability labels
        // if (daysDropdown && providerDetails.availabilityByBuckets) {
        //     daysDropdown.innerHTML = ''; // Clear existing items

        //     providerDetails.availabilityByBuckets.forEach(bucket => {
        //         const dayElement = document.createElement('span');
        //         dayElement.className = 'day dropdown-item';
        //         dayElement.textContent = bucket.label;
        //         // Store the rightDate in the element's dataset
        //         dayElement.dataset.rightDate = bucket.rightDate;
        //         daysDropdown.appendChild(dayElement);
        //     });

        //     // Set default selected value and fetch slots for the first bucket
        //     if (providerDetails.availabilityByBuckets.length > 0) {
        //         const firstBucket = providerDetails.availabilityByBuckets[0];
        //         numDaysTrigger.textContent = firstBucket.label;

        //         // Fetch slots for the default selection
        //         if (firstBucket.rightDate) {
        //             selectedDate = firstBucket.rightDate;
        //             fetchFollowupSlots(locationId, firstBucket.rightDate, providerId);
        //         }
        //     }

        //     // Add click event listeners to the new dropdown items
        //     daysDropdown.querySelectorAll('.day').forEach(item => {
        //         item.addEventListener('click', () => {
        //             numDaysTrigger.textContent = item.textContent;
        //             daysDropdown.classList.remove('active');

        //             selectedDate = item.dataset.rightDate;

        //             // Fetch slots for the selected option
        //             if (selectedDate) {
        //                 fetchFollowupSlots(locationId, selectedDate, providerId);
        //             }
        //         });
        //     });
        // }

        isProviderDetailsLoaded = true;
        checkAndEnableBookButtons();

        return providerDetails;
    } catch (error) {
        console.error('Error fetching provider details:', error);

        // Handle error state for days dropdown
        const numDaysTrigger = document.querySelector('.num-days');
        if (numDaysTrigger) {
            numDaysTrigger.classList.remove('skeleton');
            numDaysTrigger.textContent = 'Error loading';
        }

        return null;
    }
}

// Update the checkAndEnableBookButtons function
function checkAndEnableBookButtons() {
    if (isAppointmentsLoaded && isProviderDetailsLoaded) {
        removeSkeletonLoadingFromBookButtons();
    }
}

// Add this new function to create appointment
async function createAppointment() {
    try {
        // Get the selected time slot
        const selectedTimeSlot = document.querySelector('.time.selected');
        const reasonInput = document.querySelector('#appointment-reason');
        const reason = reasonInput ? reasonInput.value : '';

        if (!selectedTimeSlot) {
            throw new Error('No time slot selected');
        }

        // Parse the time slot text to get start and end times
        const timeText = selectedTimeSlot.textContent;
        const [startTimeText, endTimeText] = timeText.split(' to ');

        // Convert time to 24-hour format
        const startTime = convertTo24Hour(startTimeText);
        const endTime = convertTo24Hour(endTimeText);

        // Parse hours and minutes for startTime object
        const [hours, minutes] = startTime.split(':').map(Number);

        const response = await fetch('https://nexhealth-custom-server-*************.us-central1.run.app/api/locations/create-appointment-3', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                "locationId": String(selectedLocationId),
                "visitType": "pcp",
                "providerId": selectedProviderId,
                "startDate": selectedDate,
                "createdBy": userName,
                "source": "Careplan",
                "start_time": startTime,
                "end_time": endTime,
                "visitReason": reason || "follow-up",
                "patient_name": `${firstName} ${lastName}`,
                "startTime": {
                    "startTime": {
                        "hours": hours,
                        "minutes": minutes
                    }
                },
                "patientId": selectedPatientId
            })
        });

        const appointmentData = await response.json();

        // Check for specific error response
        if (appointmentData.status === 0 && appointmentData.error === "Failed to create appointment") {
            const errorMessage = appointmentData.details?.error?.[0] || 'Failed to book appointment. Please try again.';
            showPopup(errorMessage, true);
            return null;
        }

        trackEvent("Follow up booking", "Follow booked successfully", `Follow up booked for ${selectedPatientId}`);
        return appointmentData;
    } catch (error) {
        console.error('Error creating appointment:', error);

        // Try to parse the error response if it exists
        let errorMessage = 'Failed to book appointment. Please try again.';

        if (error.response) {
            try {
                const errorData = await error.response.json();
                if (errorData.status === 0 && errorData.error === "Failed to create appointment") {
                    errorMessage = errorData.details?.error?.[0] || errorMessage;
                }
            } catch (parseError) {
                console.error('Error parsing error response:', parseError);
            }
        }

        showPopup(errorMessage, true);
        return null;
    }
}

// Helper function to convert time to 24-hour format
function convertTo24Hour(time12h) {
    const [time, modifier] = time12h.split(' ');
    let [hours, minutes] = time.split(':');

    if (hours === '12') {
        hours = '00';
    }

    if (modifier === 'PM') {
        hours = parseInt(hours, 10) + 12;
    }

    return `${hours}:${minutes}`;
}

// Add event listener for book button
document.addEventListener('DOMContentLoaded', function () {
    const bookBtn = document.querySelector('.book-btn');
    if (bookBtn) {
        // Comment out previous event listener
        // bookBtn.addEventListener('click', async () => {
        //     console.log("clicked")
        //     try {
        //         const result = await createAppointment();
        //         if (result) {
        //             alert('Appointment booked successfully!');
        //         } else {
        //             alert('Failed to book appointment. Please try again.');
        //         }
        //     } catch (error) {
        //         console.error('Error booking appointment:', error);
        //         alert('An error occurred while booking the appointment. Please try again.');
        //     }
        // });

        // Add new event listener to open calendar popup
        bookBtn.addEventListener('click', () => {
            const today = new Date();
            const currentMonth = today.getMonth();
            const currentYear = today.getFullYear();
            const monthNames = ['January', 'February', 'March', 'April', 'May', 'June',
                'July', 'August', 'September', 'October', 'November', 'December'];

            showCalendarPopup(monthNames[currentMonth], currentYear);

            trackEvent("clicked book button", "button click", `clicked book button`);
        });
    }
});

// Add this helper function to get month index
function getMonthIndex(monthName) {
    const monthNames = ['January', 'February', 'March', 'April', 'May', 'June',
        'July', 'August', 'September', 'October', 'November', 'December'];
    return monthNames.findIndex(m => m === monthName);
}

// Add popup overlay HTML
const popupOverlay = document.createElement('div');
popupOverlay.className = 'popup-overlay';
popupOverlay.innerHTML = `
    <div class="popup-content">
        <div class="popup-header">
        </div>
        <div class="popup-body"></div>
        <div class="popup-footer">
            <button class="popup-ok-btn">OK</button>
        </div>
    </div>
`;
document.body.appendChild(popupOverlay);

// Function to show popup
function showPopup(message, isError = false) {
    const popupBody = popupOverlay.querySelector('.popup-body');
    popupBody.textContent = message;

    if (isError) {
        popupBody.style.color = '#dc3545';
    } else {
        popupBody.style.color = '#333';
    }

    popupOverlay.style.display = 'flex';

    // Focus trap
    const focusableElements = popupOverlay.querySelectorAll('button');
    const firstFocusable = focusableElements[0];
    const lastFocusable = focusableElements[focusableElements.length - 1];

    popupOverlay.addEventListener('keydown', function (e) {
        if (e.key === 'Tab') {
            if (e.shiftKey) {
                if (document.activeElement === firstFocusable) {
                    e.preventDefault();
                    lastFocusable.focus();
                }
            } else {
                if (document.activeElement === lastFocusable) {
                    e.preventDefault();
                    firstFocusable.focus();
                }
            }
        }
    });

    firstFocusable.focus();
}

// Close popup functions
function closePopup() {
    popupOverlay.style.display = 'none';
}

// Add event listeners for closing popup
popupOverlay.querySelector('.popup-ok-btn').addEventListener('click', closePopup);
popupOverlay.addEventListener('click', (e) => {
    if (e.target === popupOverlay) {
        closePopup();
    }
});

// Add new function to fetch time slots for all dates in a month
async function fetchTimeSlotsForMonth(month, year) {
    const daysInMonth = new Date(year, month + 1, 0).getDate();
    const today = new Date();
    const currentYear = today.getFullYear();
    const currentMonth = today.getMonth();
    const currentDay = today.getDate();

    // Remove has-slots class from all elements first
    document.querySelectorAll('.calendar-day').forEach(element => {
        element.classList.remove('has-slots');
    });

    // Create an array of promises for all dates in the month
    const promises = [];

    for (let day = 1; day <= daysInMonth; day++) {
        // Skip past dates
        if (year < currentYear ||
            (year === currentYear && month < currentMonth) ||
            (year === currentYear && month === currentMonth && day < currentDay)) {
            continue;
        }

        // Create date in EST timezone
        const date = new Date(year, month, day);
        // Convert to EST timezone string
        const estDate = new Date(date.toLocaleString('en-US', { timeZone: 'America/New_York' }));
        const formattedDate = estDate.toISOString().split('T')[0];

        // Add promise to array
        promises.push(
            fetchCalendarSlots(selectedLocationId, formattedDate, selectedProviderId)
                .then(slotsData => {
                    if (slotsData && !slotsData.no_date && slotsData.options && slotsData.options.length > 0) {
                        // Find the correct day element by matching the day number
                        const dayElements = document.querySelectorAll('.calendar-day:not(.inactive):not(.disabled)');
                        dayElements.forEach(element => {
                            if (parseInt(element.textContent) === day) {
                                element.classList.add('has-slots');
                            }
                        });
                    }
                    return { day, slotsData };
                })
                .catch(error => {
                    console.error(`Error fetching slots for ${formattedDate}:`, error);
                    return { day, error };
                })
        );
    }

    // Wait for all promises to resolve
    await Promise.all(promises);
}















