<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Patient Education</title>
    <link rel="stylesheet" href="./patient-education-template/css/patient-education.css">
    <link rel="stylesheet" href="./patient-education-template/css/fonts.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>

</head>

<body>

    <div id="verification-flow">
        <div class="verification-container">
            <!-- SCREEN 1: Phone Number Entry -->
            <div id="phone-entry-screen">
                <h2>Verify Your Identity</h2>
                <p>To access your care plan, please enter the phone number associated with your account.</p>
                <form id="phone-form">
                    <input type="tel" id="phone-number" placeholder="Enter your phone number" required>
                    <button type="submit" id="send-otp-btn">Send Verification Code</button>
                </form>
            </div>

            <!-- SCREEN 2: OTP Entry (Initially Hidden) -->
            <div id="otp-entry-screen" style="display: none;">
                <h2>Enter Code</h2>
                <p>We've sent a 6-digit code to your phone. Please enter it below.</p>
                <form id="otp-form">
                    <input type="text" id="otp-code" placeholder="6-Digit Code" required maxlength="6" pattern="\d{6}">
                    <button type="submit" id="verify-otp-btn">Verify and View Plan</button>
                </form>
            </div>

            <!-- Loading Spinner & Error Message -->
            <div id="loading-spinner" class="spinner"></div>
            <div id="error-message" class="error-message"></div>
        </div>
    </div>

    <div id="care-plan-content" style="display: none;">

        <section>
            <div class="container">














                <div class="upper-content">
                    <h1>
                        <span class="patient-name skeleton">

                        </span>
                    </h1>
                </div>
                <div class="content-info">
                    <div class="bt-cnt">
                        <p class="desp">This Is Your Personal Care Plan</p>
                        <button class="book-btn">Book</button>
                    </div>

                    <!-- Accordion Markup -->
                    <div class="accordion">
                        <!-- Care Gaps Accordion (Populated by fetchCareGaps) -->
                        <div class="accordion-item color-red">
                            <div class="accordion-header">
                                <div class="accordion-icon">
                                    <img src="./patient-education-template/images/care-gaps.svg" alt="care gaps"
                                        width="100">
                                </div>
                                <div class="accordion-text">
                                    <p class="title red">Care Gaps</p>
                                    <p class="subtitle">Identify and address any gaps in your healthcare for improved
                                        well‐being.</p>
                                </div>
                                <div class="accordion-arrow">
                                    <img src="./patient-education-template/images/red-arrow.svg" alt="">
                                </div>
                            </div>
                            <div class="accordion-content">
                                <!-- This content will be dynamically generated by displayCareGaps -->
                                <div id="loading-caregaps">
                                    <i class="fas fa-spinner fa-spin"></i> Loading Care gaps...
                                </div>

                            </div>
                        </div>

                        <!-- Behavioral Goals Accordion (Populated by fetchCarePlan) -->
                        <div class="accordion-item color-green ">
                            <div class="accordion-header">
                                <div class="accordion-icon">
                                    <img src="./patient-education-template/images/b-g.svg" alt="Behavioral Goals"
                                        width="100">
                                </div>
                                <div class="accordion-text">
                                    <p class="title green">Behavioral Goals</p>
                                    <p class="subtitle">Track and work towards positive changes in your habits and
                                        behaviors.</p>
                                </div>
                                <div class="accordion-arrow">
                                    <img src="./patient-education-template/images/green-arrow.svg" alt="">
                                </div>
                            </div>
                            <div class="accordion-content">
                                <!-- This content will be dynamically generated by displayBehavioralGoals -->
                                <div id="loading">
                                    <i class="fas fa-spinner fa-spin"></i> Loading Behavioral Goals.....
                                </div>

                            </div>
                        </div>

                        <!-- Education Instructions Accordion (Populated by fetchCarePlan) -->
                        <div class="accordion-item color-blue ">
                            <div class="accordion-header">
                                <div class="accordion-icon">
                                    <img src="./patient-education-template/images/edu-ins.svg"
                                        alt="Education Instructions" width="100">
                                </div>
                                <div class="accordion-text">
                                    <p class="title blue">Education Instructions</p>
                                    <p class="subtitle">Access helpful educational materials to support your healthcare
                                        journey.</p>
                                </div>
                                <div class="accordion-arrow">
                                    <img src="./patient-education-template/images/blue-arrow.svg" alt="">
                                </div>
                            </div>
                            <div class="accordion-content">
                                <!-- This content will be dynamically generated by displayEducationInstructions -->
                                <div id="loading">
                                    <i class="fas fa-spinner fa-spin"></i> Simplifying health reminders...
                                </div>

                            </div>
                        </div>
                    </div>


                </div>
            </div>
        </section>

    </div>

    <div id="tooltip" class="tooltip">
        <div class="tooltip-content">
            <div class="tooltip-header">
                <h3>Reason</h3>

            </div>
            <p id="tooltip-text"></p>
        </div>
    </div>


    <!-- Add the patient-education.js script -->
    <script src="scripts/patient-education.js"></script>

    <!-- Calendar Popup -->
    <div id="calendar-popup" class="calendar-popup">
        <div class="calendar-container">
            <div class="calendar-header">
                <button class="prev-month">&lt;</button>
                <h3>Month Year</h3>
                <div class="header-right">
                    <button class="next-month">&gt;</button>
                    <button class="close-calendar">&times;</button>
                </div>
            </div>
            <div class="calendar-grid">
                <div class="calendar-days">
                    <div class="week-wrapper">
                        <div class="day-header">S</div>
                        <div class="day-header">M</div>
                        <div class="day-header">T</div>
                        <div class="day-header">W</div>
                        <div class="day-header">T</div>
                        <div class="day-header">F</div>
                        <div class="day-header">S</div>
                    </div>
                    <div class="calendar-days-wrapper">
                        <!-- Calendar days will be dynamically populated here -->
                    </div>
                </div>
                <div class="time-slots-container">
                    <div class="loc-name">
                        <span class="loc-name-text"></span>
                    </div>
                    <div class="provider-container">
                        <span class="selected-provider dropdown-trigger skeleton">Loading...</span>
                        <div class="provider-dropdown dropdown-content">
                        </div>
                    </div>

                    <div class="time-dropdown">
                        <div class="time-slot skeleton">Loading...</div>
                    </div>
                </div>
            </div>
            <div class="calendar-footer">
                <button class="confirm-btn">Confirm</button>
            </div>
        </div>
    </div>
</body>

</html>