/* Base Styles and Variables */
:root {
    --primary-color: #2a7d8c;
    --secondary-color: #3a9eac;
    --light-color: #ffffff;
    --dark-color: #333;
    --gray-color: #888;
    --light-gray: #eee;
    --border-radius: 10px;
    --shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
    --header-bg: #f4f9fa;
    --content-bg: #fff;
}

/* Reset & Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: TodaySansNow Pro, sans-serif;
    line-height: 1.6;
    color: var(--dark-color);
    background-color: var(--light-color);
    font-size: 16px;
}

/* Container & Layout */
.container {
    width: 100%;
    max-width: 500px;
    /* or whatever width you prefer */
    margin: 0 auto;
    overflow: hidden;
}

.bt-cnt {
    display: flex;
    align-items: baseline;
    justify-content: space-between;
}

.upper-content {
    background-color: #1E3565;
    position: relative;
}

.upper-content::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 12px;
    /* Adjust this as needed */
    background-image: url(../images/bar-1.svg);
    background-repeat: no-repeat;
    background-size: cover;
    background-position: bottom;
    z-index: 1;
    /* Adjust if needed */
}

.upper-content h1 {
    font-family: TodaySansNow Pro;
    font-weight: 400;
    font-size: 35.5px;
    line-height: 42px;
    letter-spacing: 0%;
    padding: 26px 35px;
    vertical-align: middle;
    color: #F5FBF4;
    ;
}

.desp {
    font-family: TodaySansNow Pro;
    font-weight: 400;
    font-style: italic;
    font-size: 27px;
    line-height: 28.26px;
    letter-spacing: 0%;
    vertical-align: middle;
    color: #2D4B8B;
    margin-top: 25px;

}

.red {
    color: #B51313;
}

.green {
    color: #268012E0;
}

.blue {
    color: #4E72BD;
}


/* accordian style  */
/* Accordion Container */
.accordion {
    max-width: 600px;
    margin-top: 25px;
}

/* Each Item */
.accordion-item+.accordion-item {
    margin-top: 16px;

}

.accordion-item {
    border: 1px solid;
    border-radius: 18px;
}

/* Header */
.accordion-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 5px;
    border-radius: 8px;
    padding: 16px;
    cursor: pointer;
    transition: background-color 0.2s;
}

/* Color Variants */
.color-red {
    border-color: #E57373;
    background-color: #FFEDED80;
}

.color-green {
    border-color: #81C784;
    background-color: #F4FBF3;
}

.color-blue {
    border-color: #64B5F6;
    background-color: #F4F7FF;
}

.content-info {
    padding: 0px 27px;
}

/* Icon & Text */
.accordion-icon {
    margin-right: 12px;
    /* size it to match your images */
}

.accordion-text {
    flex: 1;
}

.title {
    font-family: TodaySansNow Pro;
    font-weight: 500;
    font-size: 28px;
    line-height: 100%;
    letter-spacing: 0%;
    vertical-align: middle;


}

.subtitle {
    margin-top: 5px;
    font-family: TodaySansNow Pro;
    font-weight: 300;
    font-size: 18px;
    line-height: 100%;
    letter-spacing: 0%;
    vertical-align: middle;
    line-height: 110%;
    color: #000000;

}

/* Arrow */
.accordion-arrow {
    display: inline-block;
    transition: transform 0.3s ease;
}

/* Hidden Content */
.accordion-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
    padding: 0px 20px;
}

.cns {
    margin-left: -10px;
    font-family: TodaySansNow Pro;
    font-weight: 500;
    font-size: 24px;
    line-height: 100%;
    letter-spacing: 0%;
    vertical-align: middle;
    color: #990B0B;
}

.mt {
    margin-top: 10px;
}

/* styles of list */
.timeline-red {
    position: relative;
    list-style: none;
    margin-top: 15px;
    padding: 0 10px;
    /* space for the line + a bit of breathing room */
    border-left: 1px solid #e5a8a8;
    /* your vertical line */
}

.timeline-green {
    position: relative;
    list-style: none;
    margin-top: 30px;
    padding: 0 10px;
    /* space for the line + a bit of breathing room */
    border-left: 1px solid #268012E0;
    /* your vertical line */
}

.timeline-blue {
    position: relative;
    list-style: none;
    margin-top: 50px;
    padding: 0 10px;
    /* space for the line + a bit of breathing room */
    border-left: 1px solid #4E72BD;
    /* your vertical line */
}

.timeline-item-red {
    position: relative;
    background: #f9d9d9;
    color: #8a1f1f;
    border-radius: 8px;
    padding: 12px 25px;
    margin-bottom: 12px;
}

.timeline-item-green {
    position: relative;
    background: #DDF0DA;
    color: #185E09F2;
    border-radius: 8px;
    padding: 12px 16px;
    margin-bottom: 12px;
}

.timeline-item-blue {
    position: relative;
    background: #C9D5EF;
    color: #405581;
    border-radius: 8px;
    padding: 12px 16px;
    margin-bottom: 12px;
}


.info-red {
    position: relative;
}

.info-red::before {
    content: '';
    background-image: url(../images/lucide_info-red.svg);
    width: 16px;
    height: 16px;
    position: absolute;
    top: 50%;
    right: 17px;
    transform: translateY(-50%);
    background-repeat: no-repeat;
    background-size: contain;

}

.info-green {
    position: relative;
}

.info-green::before {
    content: '';
    background-image: url(../images/lucide_info-green.svg);
    width: 16px;
    height: 16px;
    position: absolute;
    top: 50%;
    right: 17px;
    transform: translateY(-50%);
    background-repeat: no-repeat;
    background-size: contain;

}

.info-blue {
    position: relative;
}

.info-blue::before {
    content: '';
    background-image: url(../images/lucide_info-blue.svg);
    width: 16px;
    height: 16px;
    position: absolute;
    top: 50%;
    right: 17px;
    transform: translateY(-50%);
    background-repeat: no-repeat;
    background-size: contain;

}

#loading {
    text-align: center;
}

#loading {
    text-align: center;
    padding: 40px;
    font-size: 1.2rem;
    color: var(--gray-color);
}

#loading-caregaps {
    text-align: center;
    padding: 40px;
    font-size: 1.2rem;
    color: var(--gray-color);
}




@keyframes shimmer {
    0% {
        background-position: -200px 0;
    }

    100% {
        background-position: 200px 0;
    }
}

.skeleton {
    background: linear-gradient(90deg,
            #f0f0f0 25%,
            #e0e0e0 50%,
            #f0f0f0 75%);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite linear;
    border-radius: 4px;
    min-width: 90px;
    height: 40px;
    display: inline-block;
}



/* button styles*/

.book-btn,
.book-month-btn {
    font-family: TodaySansNow Pro;
    font-weight: 400;
    font-size: 20px;
    line-height: 100%;
    letter-spacing: 0%;
    vertical-align: middle;
    color: #F5FBF4;
    background-color: #395695;
    border-radius: 10px;
    padding: 9px 12px;
    padding-right: 34px;
    border: none;
    cursor: pointer;
    background-image: url("../images/calender-icon.png");
    background-repeat: no-repeat;
    background-position: right 10px center;
    background-size: 17px;
    transition: background-color 0.3s;
}

.book-btn:disabled,
.book-month-btn:disabled {
    background-color: #e0e0e0;
    cursor: not-allowed;
}

.book-btn.skeleton,
.book-month-btn.skeleton {
    background-color: #e0e0e0;
    background-image: none;
    color: transparent;
    position: relative;
    overflow: hidden;
}

.book-btn.skeleton::after,
.book-month-btn.skeleton::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: skeleton-loading 1.5s infinite;
}

@keyframes skeleton-loading {
    0% {
        transform: translateX(-100%);
    }

    100% {
        transform: translateX(100%);
    }
}

.timeline-item-red.info-red,
.timeline-item-green.info-green,
.timeline-item-blue.info-blue {
    position: relative;
    /* Required for tooltip positioning */
    cursor: help;
    /* Change cursor to indicate extra info */
}



/* Tooltip arrow */


/* Make sure info-red is included here for the hover effect */
.info-red:hover,
.info-green:hover,
.info-blue:hover {
    visibility: visible;
    opacity: 1;
}

/* --- NEW STYLES for the list inside the tooltip --- */





/* Dropdown styles */
.dropdown-trigger {
    cursor: pointer;
    position: relative;
    padding: 6px 8px;
    padding-right: 28px;
    background-color: #5C78B3;
    font-family: TodaySansNow Pro;
    font-weight: 400;
    font-size: 16px;
    line-height: 100%;
    letter-spacing: 0%;
    vertical-align: middle;
    color: #FFFFFF;
    border-radius: 10px;
}

.dropdown-trigger::after {
    content: '\f078';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    font-size: 0.7em;
    position: absolute;
    right: 6px;
    top: 50%;
    transform: translateY(-50%);
}

.dropdown-content {
    display: none;
    position: absolute;
    background-color: white;
    min-width: 85px;
    z-index: 1;
    border-radius: 8.55px;
    margin-top: 5px;
    box-shadow: 0px 0px 14.24px 2.85px #BFBFBF40;
    right: 0;
    padding: 5px;
    max-height: 150px;
    overflow: auto;
}

.dropdown-content.active {
    display: block;
}

.provider-dropdown {
    top: 28px;
}

.dropdown-item {
    padding: 4px 5px;
    text-decoration: none;
    display: block;
    cursor: pointer;
    font-family: TodaySansNow Pro;
    font-weight: 500;
    font-size: 14px;
    line-height: 15.02px;
    letter-spacing: 0%;
    text-align: center;
    vertical-align: middle;
    color: #1E3565;
    background-color: #DFE7F5;
    margin-bottom: 2px;
    border-radius: 3.75px;
}

.num-days-container,
.time-slots,
.provider-container {
    position: relative;
    font-family: TodaySansNow Pro;
    font-weight: 400;
    font-size: 22px;
    line-height: 100%;
    letter-spacing: 0%;
}


.followup-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 0 26px;
    margin-bottom: 28px;
}

.provider-container {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    width: 100%;
    position: relative;
}




/* calender styles  */

/* Calendar Popup Styles */
.calendar-popup {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.calendar-container {
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    width: 400px;
    max-width: 90%;
    overflow: hidden;
    position: relative;
    padding-top: 24px;
}

.calendar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid #eee;
}

.calendar-header h3 {
    margin: 0;
    color: #1E3565;
    font-weight: 500;
}

.calendar-header .header-right {
    display: flex;
    align-items: center;
    gap: 10px;
}

.calendar-header button {
    background: none;
    border: none;
    font-size: 18px;
    color: #1E3565;
    cursor: pointer;
    padding: 5px 10px;
}

.calendar-header .close-calendar {
    font-size: 32px;
    padding: 0;
    position: absolute;
    top: 4px;
    right: 14px;
    cursor: pointer;
}

.calendar-grid {
    display: flex;
    padding: 10px;
}

.calendar-days {
    display: flex;
    flex-direction: column;
    flex: 1;
}

.week-wrapper,
.calendar-days-wrapper {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 5px;
}

.calendar-days-wrapper {
    flex: 1;
}

.day-header {
    text-align: center;
    font-weight: 500;
    color: #1E3565;
    padding: 8px 0;
}

.calendar-day {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 22px;
    border-radius: 50%;
    cursor: pointer;
    color: #333;
}

.calendar-day.inactive {
    color: #ccc;
}

.calendar-day:hover:not(.inactive) {
    background-color: #f0f0f0;
}

.time-slots-container {
    flex: 1;
    padding: 0 10px;
}

.time-slots-container .time-dropdown {
    height: 150px;
    overflow-y: auto;
}

.time-slots-container h4 {
    margin: 0 0 10px 0;
    color: #1E3565;
    font-weight: 500;
    font-size: 16px;
    line-height: 100%;
    letter-spacing: 0%;
}

.time-slots-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.time-slot-item {
    padding: 10px;
    border-radius: 8px;
    background-color: #f0f0f0;
    cursor: pointer;
}

.time-slot-item.selected {
    background-color: #d1e7d1;
}

.time-slot-item:hover:not(.selected) {
    background-color: #e8e8e8;
}

.calendar-footer {
    padding: 15px 20px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    border-top: 1px solid #eee;
}

.confirm-btn {
    background-color: #379D90;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 8px 16px;
    font-size: 14px;
    cursor: pointer;
}

.confirm-btn:hover {
    background-color: #2a8a7d;
}

.confirm-btn.loading {
    opacity: 0.7;
    cursor: not-allowed;
}

.time-slots-container .dropdown-item,
.time-slots-container .suggested-slot {
    font-weight: 500;
    font-size: 16px;
    line-height: 15.02px;
    letter-spacing: 0%;
    text-align: center;
    vertical-align: middle;
    border-radius: 3.75px;
    background-color: #fff;
    padding: 6px 0px;
}

.time-slots-container .dropdown-item.selected,
.time-slots-container .suggested-slot.selected {
    color: #115D00;
    background-color: #115D0030;
}

.loc-name {
    color: #1E3565;
    font-weight: 500;
    font-size: 16px;
    line-height: 100%;
    letter-spacing: 0%;
    text-align: right;
    margin-right: 5px;
    margin-top: 4px;
}

.time-slots-container .provider-container {
    margin-bottom: 14px;
}

.info-icon::before {
    content: "";
    display: block;
    width: 16px;
    height: 16px;
    background-image: url("../images/info-calendar.png");
    background-size: contain;
    background-repeat: no-repeat;
    background-size: contain;
}

.month-card .task-list .info-icon::before {
    background-image: url("../images/info-calendar.png");
}

.priority-item .info-icon::before {
    background-image: url("../images/info-red.png");
}

.patient-item .info-icon::before {
    background-image: url("../images/info-pt.png");
}

.value-reason-pair .info-icon::before {
    background-image: url("../images/info-continue.png");
}

.value-reason-pair.continue .info-icon::before {
    background-image: url("../images/info-continue.png");
}

.value-reason-pair.add .info-icon::before {
    background-image: url("../images/info-add.png");
}

.value-reason-pair.change .info-icon::before {
    background-image: url("../images/info-red.png");
}

.value-reason-pair.remove .info-icon::before {
    background-image: url("../images/info-red.png");
}

.caregap-title h3 {
    font-family: TodaySansNow Pro;
    font-weight: 500;
    font-size: 20px;
    line-height: 100%;
    letter-spacing: 0%;
    color: #1E3565;
    padding: 6px 10px;
}

.caregap-section {
    margin-bottom: 20px;
}

.popup-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 9999;
    justify-content: center;
    align-items: center;
}

.popup-overlay .popup-content {
    background-color: white;
    padding: 20px;
    border-radius: 8px;
    min-width: 300px;
    max-width: 90%;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.popup-overlay .popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.popup-overlay .popup-header h3 {
    margin: 0;
    color: #1E3565;
    font-weight: 500;
}

.popup-overlay .close-popup {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #000;
}

.popup-overlay .popup-body {
    margin-bottom: 20px;
    color: #333;
}

.popup-overlay .popup-footer {
    text-align: right;
}

.popup-overlay .popup-ok-btn {
    background-color: #379D90;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 8px 16px;
    font-size: 14px;
    cursor: pointer;
}

.provider-search-container {
    padding: 8px;
    border-bottom: 1px solid #e0e0e0;
    position: sticky;
    top: 0;
    background: white;
    z-index: 1;
}

.provider-search {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    font-size: 14px;
    outline: none;
    transition: border-color 0.2s;
}

.provider-search:focus {
    border-color: #1E3565;
}

.provider-search::placeholder {
    color: #999;
}

/* Adjust provider dropdown to accommodate search */
.provider-dropdown {
    max-height: 300px;
    overflow-y: auto;
}

/* Ensure provider items have proper spacing */
.provider-dropdown .provider {
    padding: 8px 12px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.provider-dropdown .provider:hover {
    background-color: #f5f5f5;
}

.reason-input {
    margin: 15px 0;
    padding: 0 20px;
    padding-left: 0;
    width: 100%;
}

.reason-input input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.reason-input input:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.calendar-day.has-slots {
    position: relative;
    background-color: #F4FBF3;
    border-color: #F4FBF3;
}

.calendar-day.has-slots::after {
    content: '';
    position: absolute;
    bottom: 2px;
    left: 50%;
    transform: translateX(-50%);
    width: 4px;
    height: 4px;
    background-color: #185E09F2;
    border-radius: 50%;
}

.calendar-day.has-slots:hover {
    background-color: #65BD5157;
}

.calendar-day.selected {
    color: #333;
    background-color: #65BD5157;
}

/* Suggested Time Slots Styles */
.suggested-slots-section {
    padding: 10px 0;
    border-bottom: 1px solid #eee;
    margin-bottom: 10px;
}

.suggested-slots-section h4 {
    font-family: 'Inter', sans-serif;
    font-size: 14px;
    font-weight: 500;
    color: #1a1a1a;
    margin: 0 0 8px 0;
    padding: 0 15px;
}

.suggested-slots-list {
    display: flex;
    flex-direction: column;
}

.suggested-slot {
    cursor: pointer;
    transition: all 0.2s ease;
    color: #1E3565;
}

.slots-separator {
    position: relative;
    text-align: center;
    margin: 8px 0;
    padding: 0 15px;
}

.slots-separator::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background-color: #eee;
}

.slots-separator span {
    position: relative;
    background-color: white;
    padding: 0 10px;
    color: #666;
    font-size: 12px;
}

/* Update existing time slot styles */
.time.dropdown-item {
    cursor: pointer;
    transition: all 0.2s ease;
}

.slots-separator:first-of-type {
    margin-top: 0;
}


/* otp styling */

#verification-flow {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 90vh;
}

.verification-container {
    background: white;
    padding: 40px;
    border-radius: 8px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 420px;
    text-align: center;
}

.verification-container h2 {
    font-size: 24px;
    color: #333;
    margin-bottom: 10px;
}

.verification-container p {
    font-size: 16px;
    color: #666;
    margin-bottom: 25px;
}

.verification-container input {
    width: 100%;
    padding: 12px;
    margin-bottom: 20px;
    border-radius: 4px;
    border: 1px solid #ccc;
    box-sizing: border-box;
    font-size: 16px;
}

.verification-container button {
    width: 100%;
    padding: 12px;
    background-color: #395695;
    /* A nice blue */
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.2s;
}

.verification-container button:hover {
    background-color: #263d6e;
}

.verification-container button:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
}

.error-message {
    color: #d9534f;
    margin-top: 15px;
    display: none;
}

.spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    animation: spin 1s linear infinite;
    margin: 20px auto;
    display: none;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}


.tooltip {
    position: fixed;
    top: 0;
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    transform: translateX(50%);
    right: 50%;
    width: 400px;
}

.tooltip-content {
    width: 380px;
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
    padding: 20px;
    position: relative;
}



.tooltip-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid var(--light-gray);
}

.tooltip-header h3 {
    color: #1E3565;
    font-family: TodaySansNow Pro;
    font-weight: 500;
    font-size: 22px;
    line-height: 100%;
    letter-spacing: 0%;
    vertical-align: middle;
}

.close-tooltip {
    background: none;
    border: none;
    color: var(--gray-color);
    font-size: 1.2rem;
    cursor: pointer;
    transition: var(--transition);
}

.close-tooltip:hover {
    color: var(--dark-color);
}

#tooltip-text {
    font-family: TodaySansNow Pro;
    font-weight: 400;
    font-size: 18px;
    line-height: 100%;
    letter-spacing: 0%;
    vertical-align: middle;
}