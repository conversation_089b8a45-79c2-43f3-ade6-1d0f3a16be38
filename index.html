<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Care Plan</title>
        <link rel="stylesheet"
            href="./css/style.css">
        <link rel="stylesheet"
            href="./css/fonts.css">
        <!-- Font Awesome for icons -->
        <link rel="stylesheet"
            href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    </head>
    <body>
        <div class="container">
            <!-- Google Sign-in Section -->
            <div id="login-section">
                <h1>Welcome</h1>
                <p class="sign-in-text">Please sign in with Google</p>
                <div id="custom-signin-btn" class="google-btn">
                    <img src="./images/google-icon.png" alt="Google">
                    <span>Sign in</span>
                </div>
                <!-- The new Google Identity Services doesn't need this hidden button -->
            </div>
            
            <!-- Main content - hidden until authenticated -->
            <div id="app-content" style="display: none;">
                <header>
                    <div class="head">
                        <form class="search-form">
                            <input type="text" id="patient-id-input" placeholder="Patient No" value="" required>
                            <button type="submit">Search</button>
                        </form>

                        <button class="feedback-btn">Feedback</button>
                        <div class="profile-container">
                            <img src="./images/user-icon.png" alt="Profile" id="profile-btn" class="profile-btn">
                            <div id="profile-popup" class="profile-popup">
                                <div class="popup-arrow"></div>
                                <div class="popup-content">
                                    <p id="user-name" class="user-name">Loading...</p>
                                    <button id="signout-btn" class="signout-btn">Sign Out</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="patient-info-bar">
                        <div class="patient-info-left">
                            <div class="patient-pair">
                                <span class="patient-name skeleton"></span>
                                <span class="just-a-dot"></span>
                                <div class="gen-age-wrapper skeleton">
                                    <span class="patient-gender"></span>
                                    <span class="patient-age"></span>
                                </div>
                                <button class="book-btn" disabled>Book</button>
                            </div>
                            <div class="inner-wrapper">
                                <span class="patient-value skeleton"></span>
                                <button class="share-btn">Share with patient</button>
                            </div>

                            <!-- <div class="provider-container">
                                Provider:
                                
                                <span class="selected-provider dropdown-trigger skeleton">Loading...</span>
                                <div class="provider-dropdown dropdown-content">
                                </div>
                            </div> -->
                        </div>
                    </div>

                    <div class="followup-wrapper">
                        
                        <!-- <div class="num-days-container">
                             Follow-up in

                             <span class="num-days dropdown-trigger skeleton"></span>
                             <div class="days-dropdown dropdown-content">
                                <span class="day dropdown-item">7 Days</span>
                                <span class="day dropdown-item">3 Months</span>
                                <span class="day dropdown-item">6 Months</span>
                                <span class="day dropdown-item">1 Year</span>
                             </div>
                        </div>

                        <div class="time-slots">
                            at

                            <span class="time-slot dropdown-trigger skeleton"></span>
                            <div class="time-dropdown dropdown-content">
                                <span class="time dropdown-item">07:00 a.m. - 08:00 a.m.</span>
                                <span class="time dropdown-item">11:00 AM</span>
                                <span class="time dropdown-item">12:00 PM</span>
                                <span class="time dropdown-item">1:00 PM</span>
                            </div>
                        </div> -->
                    </div>
                </header>

                <main>
                    <!-- Loading indicator -->
                    <div id="loading">
                        <i class="fas fa-spinner fa-spin"></i> Loading care plan...
                    </div>

                    <!-- Accordion sections -->
                    <div class="accordion-container" id="care-plan-content"
                        style="display: none;">
                        <!-- 1. Flagged Priorities Section -->
                        <div class="accordion-section">
                            <div class="accordion-header">
                                <h2>High Risk</h2>
                                <span class="toggle-icon">
                                    <i class="fas fa-chevron-down"></i>
                                </span>
                            </div>
                            <div class="accordion-content">
                                <ul id="flagged-priorities-list"
                                    class="priorities-list"></ul>
                            </div>
                        </div>

                        <!-- 2. Provider-Facing Plan Section -->
                        <div class="accordion-section">
                            <div class="accordion-header">
                                <h2>Provider Care Plan</h2>
                                <span class="toggle-icon">
                                    <i class="fas fa-chevron-down"></i>
                                </span>
                            </div>
                            <div class="accordion-content">
                                <div class="provider-plan-subsections"
                                    id="provider-plan-container"></div>
                            </div>
                        </div>

                        <!-- 3. Patient-Facing Plan Section -->
                        <div class="accordion-section">
                            <div class="accordion-header">
                                <h2>Patient Education</h2>
                                <span class="toggle-icon">
                                    <i class="fas fa-chevron-down"></i>
                                </span>
                            </div>
                            <div class="accordion-content">
                                <div class="patient-plan-container"
                                    id="patient-plan-container"></div>
                            </div>
                        </div>

                        <!-- 4. Follow-Up Timeline Section -->
                        <div class="accordion-section">
                            <div class="accordion-header">
                                <h2>Care Timeline</h2>
                                <span class="toggle-icon">
                                    <i class="fas fa-chevron-down"></i>
                                </span>
                            </div>
                            <div class="accordion-content">
                                <div class="timeline-container"
                                    id="timeline-container"></div>
                            </div>
                        </div>
                    </div>
                </main>

                <!-- Tooltip container for showing reasons -->
                <div id="tooltip" class="tooltip">
                    <div class="tooltip-content">
                        <div class="tooltip-header">
                            <h3>Reason</h3>
                            <button class="close-tooltip">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <p id="tooltip-text"></p>
                    </div>
                </div>
            </div>
        </div>

        <script src="./scripts/script.js"></script>

        <!-- Calendar Popup -->
        <div id="calendar-popup" class="calendar-popup">
            <div class="calendar-container">
                <div class="calendar-header">
                    <button class="prev-month">&lt;</button>
                    <h3>Month Year</h3>
                    <div class="header-right">
                        <button class="next-month">&gt;</button>
                        <button class="close-calendar">&times;</button>
                    </div>
                </div>
                <div class="calendar-grid">
                    <div class="calendar-days">
                        <div class="week-wrapper">
                            <div class="day-header">S</div>
                            <div class="day-header">M</div>
                            <div class="day-header">T</div>
                            <div class="day-header">W</div>
                            <div class="day-header">T</div>
                            <div class="day-header">F</div>
                            <div class="day-header">S</div>
                        </div>
                        <div class="calendar-days-wrapper">
                            <!-- Calendar days will be dynamically populated here -->
                        </div>
                    </div>
                    <div class="time-slots-container">
                        <div class="loc-name">
                            <span class="loc-name-text"></span>
                        </div>
                        <div class="provider-container">
                            <span class="selected-provider dropdown-trigger skeleton">Loading...</span>
                            <div class="provider-dropdown dropdown-content">
                            </div>
                        </div>
                        
                        <div class="time-dropdown">
                            <div class="time-slot skeleton">Loading...</div>
                        </div>
                    </div>
                </div>
                <div class="calendar-footer">
                    <button class="confirm-btn">Confirm</button>
                </div>
            </div>
        </div>

        
    </body>
</html>








