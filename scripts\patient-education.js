let patientId;
let firstName;
let lastName;
let phoneNumber;
let patientAppointments = [];
const loadingElement = document.getElementById('loading');
const loadingCaregaps = document.getElementById('loading-caregaps');
let selectedProviderId = null;
let selectedProviderName = null;
let selectedLocationId = null;
let selectedPatientId = null;
let selectedLocationName = null;
let selectedDate = null;
let isAppointmentsLoaded = false;
let isProviderDetailsLoaded = false;
let userName = '';
let email;
const tooltipElement = document.getElementById('tooltip');
const tooltipTitleElement = document.querySelector('#tooltip .tooltip-header h3');
const tooltipTextElement = document.getElementById('tooltip-text');






const locMap = [
    {
        locationName: "AST",
        id: [233163, 233164],
        text: "Astoria",
    },
    {
        locationName: "BRTW",
        id: [233165, 233166],
        text: "Bronx Bartow",
    },
    {
        locationName: "BX174",
        text: "Bronx 174th St",
        id: [233167, 233168],
    },
    {
        locationName: "CH",
        text: "Crown Heights",
        id: [233170, 233171],
    },
    {
        locationName: "DWNT",
        text: "StuyTown",
        id: [233172, 233192],
    },
    {
        id: [233174],
        text: "Hicksville",
        locationName: "HIX",
    },
    {
        id: [233175, 233176],
        text: "Jackson Heights",
        locationName: "JH",
    },
    {
        text: "Jamaica",
        id: [233177, 233178],
        locationName: "JAM",
    },
    {
        text: "Long Island City",
        id: [233179, 233193],
        locationName: "LIC",
    },
    {
        id: [233183, 233194],
        text: "Mineola",
        locationName: "MIN",
    },
    {
        text: "Williamsburg",
        id: [233195, 233213],
        locationName: "WIL",
    }
];

function getMonthName(monthIndex) {
    const monthNames = ['January', 'February', 'March', 'April', 'May', 'June',
        'July', 'August', 'September', 'October', 'November', 'December'];
    return monthNames[monthIndex];
}

function getMonthIndex(monthName) {
    const monthNames = ['January', 'February', 'March', 'April', 'May', 'June',
        'July', 'August', 'September', 'October', 'November', 'December'];
    return monthNames.findIndex(m => m === monthName);
}
function getSuggestedTimeSlots(slots) {
    if (!slots || !slots.options || slots.options.length === 0) {
        return [];
    }

    // Sort slots by time
    const sortedSlots = [...slots.options].sort((a, b) => {
        const timeA = a.text.split(' to ')[0];
        const timeB = b.text.split(' to ')[0];
        return timeA.localeCompare(timeB);
    });

    // If only 1 slot, return empty array (no suggestions)
    if (sortedSlots.length === 1) {
        return [];
    }

    // If 2 slots, return 1 suggestion
    if (sortedSlots.length === 2) {
        return [sortedSlots[0]];
    }

    // If 3 slots, return 2 suggestions
    if (sortedSlots.length === 3) {
        return [sortedSlots[0], sortedSlots[1]];
    }

    // For 4 or more slots, implement spacing-based selection
    const timeToMinutes = (timeStr) => {
        const [time, period] = timeStr.split(' ');
        let [hours, minutes] = time.split(':').map(Number);
        if (period === 'PM' && hours !== 12) hours += 12;
        if (period === 'AM' && hours === 12) hours = 0;
        return hours * 60 + minutes;
    };

    // Calculate spacing scores for each slot
    const slotsWithScores = sortedSlots.map((slot, index) => {
        const slotTime = timeToMinutes(slot.text.split(' to ')[0]);
        let totalSpacing = 0;
        let spacingCount = 0;

        // Calculate average spacing from all other slots
        sortedSlots.forEach((otherSlot, otherIndex) => {
            if (index !== otherIndex) {
                const otherTime = timeToMinutes(otherSlot.text.split(' to ')[0]);
                const spacing = Math.abs(slotTime - otherTime);
                totalSpacing += spacing;
                spacingCount++;
            }
        });

        const averageSpacing = totalSpacing / spacingCount;

        // Bonus for morning slots (before 12 PM)
        const isMorning = slotTime < 12 * 60;
        const morningBonus = isMorning ? 30 : 0;

        return {
            slot,
            score: averageSpacing + morningBonus
        };
    });

    // Sort slots by score (higher is better)
    slotsWithScores.sort((a, b) => b.score - a.score);

    // Select slots ensuring good distribution
    const selectedSlots = [];
    const minSpacing = 60; // Minimum 1 hour between slots

    for (const { slot } of slotsWithScores) {
        if (selectedSlots.length >= 4) break;

        const slotTime = timeToMinutes(slot.text.split(' to ')[0]);

        // Check if this slot is far enough from already selected slots
        const isWellSpaced = selectedSlots.every(selectedSlot => {
            const selectedTime = timeToMinutes(selectedSlot.text.split(' to ')[0]);
            return Math.abs(slotTime - selectedTime) >= minSpacing;
        });

        if (isWellSpaced) {
            selectedSlots.push(slot);
        }
    }

    // If we couldn't find 4 well-spaced slots, add more slots
    if (selectedSlots.length < 4) {
        for (const { slot } of slotsWithScores) {
            if (selectedSlots.length >= 4) break;
            if (!selectedSlots.includes(slot)) {
                selectedSlots.push(slot);
            }
        }
    }

    return selectedSlots;
}


document.addEventListener('click', function (event) {
    if (event.target.classList.contains('selected-provider')) {
        document.querySelector(".provider-dropdown").classList.toggle('active');
    }
    if (!event.target.closest('.provider-container')) {
        document.querySelector(".provider-dropdown").classList.remove('active');
    }
});
// =========================================================================
// MAIN INITIALIZATION LOGIC
// This is the single entry point that runs when the page loads.
// =========================================================================

async function trackEvent(eventName, eventCategory, eventLabel) {
    const payload = {
        "event-name": eventName,
        "event_category": eventCategory,
        "event_label": eventLabel,
        "patient_acct_no": patientId,
        "providerName": firstName + " " + lastName,
        "providerId": selectedProviderId,
        "userName": userName,
        "email": email
    };

    const response = await fetch('/api/event_tracker', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload)
    });
}



document.addEventListener('DOMContentLoaded', async () => {

    const bookBtn = document.querySelector('.book-btn');
    if (bookBtn) {

        // Add new event listener to open calendar popup
        bookBtn.addEventListener('click', () => {
            const today = new Date();
            const currentMonth = today.getMonth();
            const currentYear = today.getFullYear();
            const monthNames = ['January', 'February', 'March', 'April', 'May', 'June',
                'July', 'August', 'September', 'October', 'November', 'December'];

            showCalendarPopup(monthNames[currentMonth], currentYear);

            trackEvent("clicked book button", "button click", `clicked book button`);
        });
    }


    setupAccordionListeners();
    // 1. Immediately show loading messages to the user.
    // setLoadingState();

    // 2. Get the encrypted patient ID from the URL.
    const encryptedPatientId = getUrlParameter('pid');

    if (!encryptedPatientId) {
        handleFatalError("No patient identifier was found in the link.");
        return;
    }

    try {
        // 3. Decrypt the ID. This MUST complete before we do anything else.
        patientId = await decryptPatientId(encryptedPatientId);

        if (!patientId) {
            // This handles the case where decryption returns null or fails.
            throw new Error('Failed to validate patient identifier.');
        }

        console.log("Decrypted Patient ID:", patientId);


        // 4. Now that we have the real patient ID, fetch both care plan and care gaps in parallel.
        // Promise.all is much faster as it runs both fetches at the same time and waits for both to finish.
        await Promise.all([
            fetchCarePlan(),
            fetchCareGaps(),
            fetchPatientAppointments(patientId)

        ]);

        // 5. All data is loaded and displayed, now set up interactive elements like accordions.


    } catch (error) {
        console.error('Error during page initialization:', error);
        handleFatalError("Could not load your care plan. Please try the link again or contact your provider.");
    }
});



function generateCalendarDays(month, year) {
    const date = new Date(year, month, 1);
    const daysInMonth = new Date(year, month + 1, 0).getDate();
    const firstDayIndex = date.getDay();

    let daysHTML = '';

    // Get current date for comparison
    const today = new Date();
    const currentYear = today.getFullYear();
    const currentMonth = today.getMonth();
    const currentDay = today.getDate();

    // Add empty cells for days before the 1st of the month
    for (let i = 0; i < firstDayIndex; i++) {
        const prevMonth = month === 0 ? 11 : month - 1;
        const prevYear = month === 0 ? year - 1 : year;
        const daysInPrevMonth = new Date(prevYear, prevMonth + 1, 0).getDate();
        const day = daysInPrevMonth - firstDayIndex + i + 1;
        daysHTML += `<div class="calendar-day inactive">${day}</div>`;
    }

    // Add days of the current month
    for (let i = 1; i <= daysInMonth; i++) {
        // Check if the date is in the past
        const isPastDate = (year < currentYear) ||
            (year === currentYear && month < currentMonth) ||
            (year === currentYear && month === currentMonth && i < currentDay);

        // Set default selected date to current day
        const isDefaultDate = i === currentDay && !isPastDate;

        const classes = ['calendar-day'];
        if (isPastDate) {
            classes.push('disabled');
            classes.push('inactive');
        }
        if (isDefaultDate) {
            classes.push('selected');
        }

        daysHTML += `<div class="${classes.join(' ')}">${i}</div>`;
    }

    const selectedDate = new Date(year, month, currentDay);
    // Convert to EST timezone
    const estDate = new Date(selectedDate.toLocaleString('en-US', { timeZone: 'America/New_York' }));
    const formattedDate = estDate.toISOString().split('T')[0];

    // Fetch slots for the first day of the month
    fetchCalendarSlots(selectedLocationId, formattedDate, selectedProviderId);

    // Add days for the next month to fill the grid
    const totalCells = Math.ceil((firstDayIndex + daysInMonth) / 7) * 7;
    const remainingCells = totalCells - (firstDayIndex + daysInMonth);

    for (let i = 1; i <= remainingCells; i++) {
        daysHTML += `<div class="calendar-day inactive">${i}</div>`;
    }

    return daysHTML;
}


function showCalendarPopup(monthName, year) {
    // Get the existing popup container
    const calendarPopup = document.getElementById('calendar-popup');
    if (!calendarPopup) return;

    // Get month number from name
    const monthNames = ['January', 'February', 'March', 'April', 'May', 'June',
        'July', 'August', 'September', 'October', 'November', 'December'];
    const monthIndex = monthNames.findIndex(m => monthName.includes(m));
    const month = monthIndex !== -1 ? monthIndex : new Date().getMonth();

    // Update the month and year in the header
    const headerTitle = calendarPopup.querySelector('.calendar-header h3');
    if (headerTitle) {
        headerTitle.textContent = `${monthNames[month]} ${year}`;
    }

    // Update the calendar days
    const calendarDaysWrapper = calendarPopup.querySelector('.calendar-days-wrapper');
    if (calendarDaysWrapper) {
        // Clear the wrapper before adding new days
        calendarDaysWrapper.innerHTML = '';
        // Add the generated days
        calendarDaysWrapper.innerHTML = generateCalendarDays(month, year);
    }

    // Add reason input field if it doesn't exist
    let reasonInput = calendarPopup.querySelector('.reason-input');
    if (!reasonInput) {
        reasonInput = document.createElement('div');
        reasonInput.className = 'reason-input';
        reasonInput.innerHTML = `
            <input type="text" id="appointment-reason" placeholder="Enter reason for visit">
        `;
        // Insert before the confirm button
        const confirmBtn = calendarPopup.querySelector('.confirm-btn');
        if (confirmBtn) {
            confirmBtn.parentNode.insertBefore(reasonInput, confirmBtn);
        }
    }

    // Show the popup
    calendarPopup.style.display = 'flex';

    // Add event listeners
    setupCalendarEventListeners(calendarPopup, month, parseInt(year));

    // Fetch time slots for all dates in the month in parallel
    fetchTimeSlotsForMonth(month, year);
}


function addSkeletonLoadingToBookButtons() {
    // Add skeleton class to all book buttons
    document.querySelectorAll('.book-btn, .book-month-btn').forEach(btn => {
        btn.classList.add('skeleton');
        btn.disabled = true;
    });
}


function checkAndEnableBookButtons() {
    if (isAppointmentsLoaded && isProviderDetailsLoaded) {
        removeSkeletonLoadingFromBookButtons();
    }
}

function removeSkeletonLoadingFromBookButtons() {
    // Remove skeleton class from all book buttons
    document.querySelectorAll('.book-btn, .book-month-btn').forEach(btn => {
        btn.classList.remove('skeleton');
        btn.disabled = false;
    });
}


// =========================================================================
// HELPER FUNCTIONS (URL, Decryption, Error Handling, UI State)
// =========================================================================

function setupCalendarEventListeners(popup, currentMonth, currentYear) {
    // Close popup when clicking outside
    popup.addEventListener('click', function (e) {
        if (e.target === popup) {
            popup.style.display = 'none';
        }
    });

    // Close button
    popup.querySelector('.close-calendar').addEventListener('click', function () {
        popup.style.display = 'none';
    });

    // Previous month button
    popup.querySelector('.prev-month').addEventListener('click', function () {
        let newMonth = currentMonth - 1;
        let newYear = currentYear;

        if (newMonth < 0) {
            newMonth = 11;
            newYear--;
        }

        showCalendarPopup(getMonthName(newMonth), newYear);
    });

    // Next month button
    popup.querySelector('.next-month').addEventListener('click', function () {
        let newMonth = currentMonth + 1;
        let newYear = currentYear;

        if (newMonth > 11) {
            newMonth = 0;
            newYear++;
        }

        showCalendarPopup(getMonthName(newMonth), newYear);
    });

    // Day selection
    popup.querySelectorAll('.calendar-day:not(.inactive):not(.disabled)').forEach(day => {
        day.addEventListener('click', function () {
            // Remove selected class from all days
            popup.querySelectorAll('.calendar-day').forEach(d => d.classList.remove('selected'));
            // Add selected class to clicked day
            this.classList.add('selected');

            // Get selected date
            const selectedDay = parseInt(this.textContent);
            const selectedDate = new Date(currentYear, currentMonth, selectedDay);
            // Convert to EST timezone
            const estDate = new Date(selectedDate.toLocaleString('en-US', { timeZone: 'America/New_York' }));
            const formattedDate = estDate.toISOString().split('T')[0];

            // Show skeleton loading for time slots
            const timeDropdown = popup.querySelector('.time-dropdown');
            timeDropdown.innerHTML = '<div class="time-slot skeleton">Loading...</div>';

            // Fetch slots for the selected date
            fetchCalendarSlots(selectedLocationId, formattedDate, selectedProviderId);
        });
    });

    // Confirm button - Remove existing listeners and add new one
    const confirmBtn = popup.querySelector('.confirm-btn');
    const newConfirmBtn = confirmBtn.cloneNode(true);
    confirmBtn.parentNode.replaceChild(newConfirmBtn, confirmBtn);

    newConfirmBtn.addEventListener('click', async function () {
        const selectedDay = popup.querySelector('.calendar-day.selected')?.textContent;
        const selectedTime = popup.querySelector('.time.selected')?.textContent;
        const reasonInput = popup.querySelector('#appointment-reason');
        const reason = reasonInput ? reasonInput.value : '';

        if (selectedDay && selectedTime) {
            try {
                // Disable confirm button and show loading state
                const confirmBtn = this;
                const originalBtnText = confirmBtn.textContent;
                confirmBtn.disabled = true;
                confirmBtn.textContent = 'Booking...';
                confirmBtn.classList.add('loading');

                // Format the selected date from calendar and convert to EST
                const selectedDate = new Date(currentYear, currentMonth, parseInt(selectedDay));
                const estDate = new Date(selectedDate.toLocaleString('en-US', { timeZone: 'America/New_York' }));
                const formattedDate = estDate.toISOString().split('T')[0];

                // Parse the time slot text to get start and end times
                const [startTimeText, endTimeText] = selectedTime.split(' to ');

                // Convert time to 24-hour format
                const startTime = convertTo24Hour(startTimeText);
                const endTime = convertTo24Hour(endTimeText);

                // Parse hours and minutes for startTime object
                const [hours, minutes] = startTime.split(':').map(Number);

                // Create appointment payload
                const appointmentPayload = {
                    "locationId": String(selectedLocationId),
                    "visitType": "pcp",
                    "providerId": selectedProviderId,
                    "startDate": formattedDate,
                    "createdBy": selectedProviderName,
                    "source": "Careplan",
                    "start_time": startTime,
                    "end_time": endTime,
                    "visitReason": reason || "follow-up",
                    "patient_name": `${firstName} ${lastName}`,
                    "startTime": {
                        "startTime": {
                            "hours": hours,
                            "minutes": minutes
                        }
                    },
                    "patientId": selectedPatientId
                };

                // Make API call to create appointment
                const response = await fetch('https://nexhealth-custom-server-1081644116068.us-central1.run.app/api/locations/create-appointment-3', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(appointmentPayload)
                });


                const appointmentData = await response.json();

                // Check for specific error response
                if (appointmentData.status === 0 && appointmentData.error === "Failed to create appointment") {
                    // Get the specific error message from the details.error array if available
                    const errorMessage = appointmentData.details?.error?.[0] || 'Failed to book appointment. Please try again.';
                    showPopup(errorMessage, true);
                } else {
                    showPopup('Appointment booked successfully!');

                    // Refresh time slots for the current date
                    const timeDropdown = popup.querySelector('.time-dropdown');
                    const timeSlotTrigger = popup.querySelector('.time-slot');

                    if (timeDropdown) {
                        timeDropdown.innerHTML = '<div class="time-slot skeleton">Loading...</div>';
                    }
                    if (timeSlotTrigger) {
                        timeSlotTrigger.textContent = 'Loading...';
                        timeSlotTrigger.classList.add('skeleton');
                    }

                    // Fetch updated slots
                    await fetchCalendarSlots(selectedLocationId, formattedDate, selectedProviderId);
                }
            } catch (error) {
                console.error('Error creating appointment:', error);

                // Try to parse the error response if it exists
                let errorMessage = 'Failed to book appointment. Please try again.';

                if (error.response) {
                    try {
                        const errorData = await error.response.json();
                        if (errorData.status === 0 && errorData.error === "Failed to create appointment") {
                            errorMessage = errorData.details?.error?.[0] || errorMessage;
                        }
                    } catch (parseError) {
                        console.error('Error parsing error response:', parseError);
                    }
                }

                showPopup(errorMessage, true);
            } finally {
                // Reset confirm button state
                const confirmBtn = this;
                confirmBtn.disabled = false;
                confirmBtn.textContent = 'Confirm';
                confirmBtn.classList.remove('loading');
            }
        } else {
            showPopup('Please select both a day and time slot', true);
        }
    });
}




function getUrlParameter(name) {
    name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
    const regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
    const results = regex.exec(location.search);
    return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
}

async function fetchProviderDetails(providerName, patientId, locationId, providerId, locationName) {
    try {


        const response = await fetch('https://nexhealth-custom-server-1081644116068.us-central1.run.app/api/internal/fetch-the-provider', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                "providerName": providerName,
                "patientLocationKey": locationName,
                "patientNexId": patientId,
                "locationId": locationId,
                "providerId": providerId,
                "customDate": "",
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }

        providerDetails = await response.json();
        isProviderDetailsLoaded = true;
        checkAndEnableBookButtons();

        return providerDetails;
    } catch (error) {
        console.error('Error fetching provider details:', error);

        // Handle error state for days dropdown
        const numDaysTrigger = document.querySelector('.num-days');
        if (numDaysTrigger) {
            numDaysTrigger.classList.remove('skeleton');
            numDaysTrigger.textContent = 'Error loading';
        }

        return null;
    }
}



async function fetchPatientAppointments(patientId) {
    try {
        // Add skeleton loading to book buttons
        addSkeletonLoadingToBookButtons();

        // Show skeleton loading state


        const response = await fetch('https://nexhealth-custom-server-1081644116068.us-central1.run.app/api/follow/many-appts', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                "eCWId": patientId
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }

        patientAppointments = await response.json();

        // Update the provider dropdown with the fetched data
        updateProviderDropdown(patientAppointments);

        isAppointmentsLoaded = true;
        checkAndEnableBookButtons();

        // Remove skeleton loading from book buttons
        removeSkeletonLoadingFromBookButtons();

        return patientAppointments;
    } catch (error) {
        console.error('Error fetching patient appointments:', error);

        // Reset skeleton on error
        const selectedProviderElement = document.querySelector('.selected-provider');
        selectedProviderElement.classList.remove('skeleton');
        selectedProviderElement.textContent = 'Error loading providers';

        // Remove skeleton loading from book buttons
        removeSkeletonLoadingFromBookButtons();

        return [];
    }
}

async function decryptPatientId(encryptedId) {
    try {
        const response = await fetch('/decrypt', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ encrypted: encryptedId })
        });
        if (!response.ok) {
            console.error(`Decryption failed with status: ${response.status}`);
            return null;
        }
        const { decrypted } = await response.json();
        return decrypted;
    } catch (error) {
        console.error('Failed to decrypt patient ID:', error);
        return null;
    }
}

function setLoadingState() {
    document.querySelector('.accordion-item.color-red .accordion-content').innerHTML = `  <div id="loading">
                        <i class="fas fa-spinner fa-spin"></i> Loading care plan...
                    </div>`;
    document.querySelector('.accordion-item.color-green .accordion-content').innerHTML = `  <div id="loading">
                        <i class="fas fa-spinner fa-spin"></i> Loading care plan...
                    </div>`;
    document.querySelector('.accordion-item.color-blue .accordion-content').innerHTML = `  <div id="loading">
                        <i class="fas fa-spinner fa-spin"></i> Loading care plan...
                    </div>`;
}

function handleFatalError(message) {
    // A simple way to show a user-friendly error is to replace the body.
    // Make sure you have a CSS class for .error-container for better styling.
    document.body.innerHTML = `<div class="error-container"><h1>Error</h1><p>${message}</p></div>`;
}

function setupAccordionListeners() {
    document.querySelectorAll('.accordion-item').forEach(item => {
        const header = item.querySelector('.accordion-header');
        const content = item.querySelector('.accordion-content');
        const arrow = header.querySelector('.accordion-arrow');

        // --- Function to open/close accordion (to avoid repeating code) ---
        const setItemState = (isActive) => {
            item.classList.toggle('active', isActive);
            if (isActive) {
                content.style.maxHeight = content.scrollHeight + 'px';
                if (arrow) arrow.style.transform = 'rotate(180deg)';
            } else {
                content.style.maxHeight = null;
                if (arrow) arrow.style.transform = '';
            }
        };

        // --- Check initial state on page load ---
        // if (item.classList.contains('active')) {
        //     setItemState(true);
        // }

        // --- Click listener for user interaction ---
        header.addEventListener('click', () => {
            setItemState(!item.classList.contains('active'));
        });

        // --- NEW: AUTOMATIC RESIZING WITH MUTATION OBSERVER ---
        // Create an observer to watch for changes in the content
        // const observer = new MutationObserver(() => {
        //     // If the item is currently active (open), recalculate its height
        //     if (item.classList.contains('active')) {
        //         content.style.maxHeight = content.scrollHeight + 'px';
        //     }
        // });

        // // Start observing the content element for changes to its children
        // // `childList: true` watches for added/removed nodes (like your content)
        // // `subtree: true` watches all descendants, not just direct children
        // observer.observe(content, { childList: true, subtree: true });
    });
}


function reassembleJson(originalJson, simplifiedResults, context) {
    if (!simplifiedResults || simplifiedResults.length === 0) {
        return originalJson; // Nothing to do, return original data
    }

    const simplifiedMap = new Map(simplifiedResults.map(item => [item.id, item.simplified_text]));

    if (context === 'care_gaps') {
        originalJson.careGaps.forEach((gap, index) => {
            const id = `careGap_${index}`;
            if (simplifiedMap.has(id)) {
                gap.description = simplifiedMap.get(id);
            }
        });
    } else if (context === 'care_plan') {
        const plan = originalJson.care_plan?.patient_facing_plan;
        if (plan) {
            plan.behavioral_goals?.forEach((goal, goalIndex) => {
                goal.item.forEach((_, itemIndex) => {
                    const id = `behavioral_${goalIndex}_${itemIndex}`;
                    if (simplifiedMap.has(id)) {
                        goal.item[itemIndex] = simplifiedMap.get(id);
                    }
                });
            });
            plan.education_instructions?.forEach((instr, instrIndex) => {
                instr.item.forEach((_, itemIndex) => {
                    const id = `education_${instrIndex}_${itemIndex}`;
                    if (simplifiedMap.has(id)) {
                        instr.item[itemIndex] = simplifiedMap.get(id);
                    }
                });
            });
        }
    }
    return originalJson;
}






// =========================================================================
// AI SIMPLIFICATION FUNCTION
// =========================================================================

async function simplifyJsonWithAI(jsonData, context) {
    try {
        const response = await fetch('/ai/simplify-json', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ json_data: jsonData, context: context })
        });
        if (!response.ok) {
            console.error('AI JSON simplification failed. The page will display the original text.');
            return []; // **CHANGED:** Return an empty array on failure for graceful fallback.
        }
        return await response.json(); // Returns the list of simplified texts
    } catch (error) {
        console.error('Error calling JSON simplification API:', error);
        return []; // **CHANGED:** Return an empty array on failure.
    }
}

function updateProviderDropdown(appointments) {
    const providerDropdown = document.querySelector('.provider-dropdown');
    const selectedProviderElement = document.querySelector('.selected-provider');

    // Clear existing dropdown items
    providerDropdown.innerHTML = '';

    // Add search input field
    const searchContainer = document.createElement('div');
    searchContainer.className = 'provider-search-container';
    searchContainer.innerHTML = `
        <input type="text" class="provider-search" placeholder="Search providers...">
    `;
    providerDropdown.appendChild(searchContainer);

    // Add search functionality
    const searchInput = searchContainer.querySelector('.provider-search');
    searchInput.addEventListener('input', function (e) {
        const searchTerm = e.target.value.toLowerCase();
        const providerItems = providerDropdown.querySelectorAll('.provider');

        providerItems.forEach(item => {
            const providerName = item.textContent.toLowerCase();
            if (providerName.includes(searchTerm)) {
                item.style.display = '';
            } else {
                item.style.display = 'none';
            }
        });
    });

    // Remove skeleton class and loading text
    selectedProviderElement.classList.remove('skeleton');
    selectedProviderElement.textContent = '';

    // Hide all book buttons if appointmentsByLocation is empty
    if (!appointments.appointmentsByLocation || appointments.appointmentsByLocation.length === 0) {
        document.querySelectorAll('.book-btn').forEach(btn => {
            btn.style.display = 'none';
        });
        document.querySelectorAll('.book-month-btn').forEach(btn => {
            btn.style.display = 'none';
        });
        selectedProviderElement.textContent = 'No providers';
        selectedProviderId = null;
        selectedProviderName = null;
        selectedLocationId = null;
        selectedPatientId = null;
        return;
    }

    // Show all book buttons if we have appointments
    document.querySelectorAll('.book-btn').forEach(btn => {
        btn.style.display = 'block';
    });
    document.querySelectorAll('.book-month-btn').forEach(btn => {
        btn.style.display = 'block';
    });

    // Keep track of added providers to avoid duplicates
    const addedProviders = new Set();

    // Add providers from appointments to dropdown
    appointments.appointmentsByLocation.forEach(item => {
        const providerName = item.appointment.provider_name;
        const providerId = item.appointment.provider_id;
        const locationId = item.appointment.location_id;
        const patientId = item.appointment.patient_id;
        const locationName = item.locationName;

        // Create a unique key combining provider name and ID
        const providerKey = `${providerName}-${providerId}`;

        // Skip if this provider has already been added
        if (addedProviders.has(providerKey)) {
            return;
        }

        // Add to our tracking set
        addedProviders.add(providerKey);

        const providerElement = document.createElement('span');
        providerElement.className = 'provider dropdown-item';
        providerElement.textContent = providerName;

        // Store provider data as attributes
        providerElement.dataset.providerId = providerId;
        providerElement.dataset.providerName = providerName;
        providerElement.dataset.locationId = locationId;
        providerElement.dataset.patientId = patientId;
        providerElement.dataset.locationName = locationName;

        // Add click event to update selected provider
        providerElement.addEventListener('click', function () {
            // Update the displayed provider name
            selectedProviderElement.textContent = providerName;

            // Update global variables with selected provider details
            selectedProviderId = providerId;
            selectedProviderName = providerName;
            selectedLocationId = locationId;
            selectedPatientId = patientId;
            selectedLocationName = locationName;

            // Find and set the initial location name from locMap
            const locationInfo = locMap.find(loc => loc.locationName === locationName);
            if (locationInfo) {
                const locNameText = document.querySelector('.loc-name-text');
                if (locNameText) {
                    locNameText.textContent = locationInfo.text;
                }
            }

            // Fetch provider details with the selected provider
            fetchProviderDetails(providerName, patientId, String(locationId), providerId, locationName);

            // If calendar popup is open, fetch new slots for the selected date
            const calendarPopup = document.getElementById('calendar-popup');
            if (calendarPopup && calendarPopup.style.display === 'flex') {
                const selectedDay = calendarPopup.querySelector('.calendar-day.selected')?.textContent;
                if (selectedDay) {
                    // Show skeleton loading for time slots
                    const timeDropdown = calendarPopup.querySelector('.time-dropdown');
                    const timeSlotTrigger = calendarPopup.querySelector('.time-slot');
                    if (timeDropdown) {
                        timeDropdown.innerHTML = '<div class="time-slot skeleton">Loading...</div>';
                    }
                    if (timeSlotTrigger) {
                        timeSlotTrigger.textContent = 'Loading...';
                        timeSlotTrigger.classList.add('skeleton');
                    }

                    // Get current month and year from header
                    const headerTitle = calendarPopup.querySelector('.calendar-header h3').textContent;
                    const [monthName, year] = headerTitle.split(' ');
                    const monthIndex = getMonthIndex(monthName);

                    // Format date for API
                    const selectedDate = new Date(parseInt(year), monthIndex, parseInt(selectedDay));
                    const formattedDate = selectedDate.toISOString().split('T')[0];

                    // Fetch new slots for the selected date
                    fetchCalendarSlots(locationId, formattedDate, providerId);

                    fetchTimeSlotsForMonth(monthIndex, parseInt(year));
                }
            }

            // Hide dropdown after selection
            providerDropdown.classList.remove('active');
        });

        providerDropdown.appendChild(providerElement);
    });

    // If we have providers, set the first one as default
    if (appointments.appointmentsByLocation.length > 0) {
        const firstProvider = appointments.appointmentsByLocation[0].appointment;
        selectedProviderElement.textContent = firstProvider.provider_name;
        selectedProviderId = firstProvider.provider_id;
        selectedProviderName = firstProvider.provider_name;
        selectedLocationId = firstProvider.location_id;
        selectedPatientId = firstProvider.patient_id;
        selectedLocationName = appointments.appointmentsByLocation[0].locationName;

        // Find and set the initial location name from locMap
        const locationInfo = locMap.find(loc => loc.locationName === selectedLocationName);
        if (locationInfo) {
            const locNameText = document.querySelector('.loc-name-text');
            if (locNameText) {
                locNameText.textContent = locationInfo.text;
            }
        }

        // Fetch provider details for the default provider
        fetchProviderDetails(
            firstProvider.provider_name,
            firstProvider.patient_id,
            String(firstProvider.location_id),
            firstProvider.provider_id,
            selectedLocationName
        );

    } else {
        // If no providers found, show a message
        selectedProviderElement.textContent = 'No providers';
        selectedProviderId = null;
        selectedProviderName = null;
        selectedLocationId = null;
        selectedPatientId = null;
    }
}
// =========================================================================
// API FETCH AND DISPLAY FUNCTIONS
// (These functions are now guaranteed to have a valid `patientId`)
// =========================================================================

async function fetchCarePlan() {
    try {
        const patientName = document.querySelector('.patient-name');
        patientName.innerHTML = "";
        patientName.classList.add('skeleton');




        const patInfoUrl = `/userinfoapi/get-patinfo?pid=${patientId}`;
        const patInfoResponse = await fetch(patInfoUrl);
        const responseText = await patInfoResponse.json();

        // Replace NaN with null before parsing
        const sanitizedResponse = responseText.replace(/:\s*NaN/g, ': null');
        const patientInfo = JSON.parse(sanitizedResponse);

        phoneNumber = patientInfo.data.patient_cell_phone || patientInfo.data.patient_home_phone || patientInfo.data.patient_work_phone || null;

        firstName = patientInfo.data.patient_first_name.charAt(0).toUpperCase() +
            patientInfo.data.patient_first_name.slice(1).toLowerCase();
        lastName = patientInfo.data.patient_last_name.charAt(0).toUpperCase() +
            patientInfo.data.patient_last_name.slice(1).toLowerCase();


        patientName.textContent = `Hi ${firstName} ${lastName}`;
        patientName.classList.remove('skeleton');




        const url = `/api/get-careplan?patient_id=${patientId}`;











        const response = await fetch(url, { method: 'GET', headers: { 'Content-Type': 'application/json' } });
        if (!response.ok) throw new Error(`HTTP Error: ${response.status}`);

        const carePlanData = await response.json();




        const simplifiedPlan = await simplifyJsonWithAI(carePlanData, 'care_plan');
        const finalPlan = reassembleJson(carePlanData, simplifiedPlan, 'care_plan');
        const plan = finalPlan.care_plan?.patient_facing_plan;




        if (plan) {

            displayBehavioralGoals(plan.behavioral_goals || []);
            displayEducationInstructions(plan.education_instructions || []);
            loadingElement.style.display = 'none';
        } else {
            document.querySelector('.accordion-item.color-green .accordion-content').textContent = 'No goals available.';
            document.querySelector('.accordion-item.color-blue .accordion-content').textContent = 'No instructions available.';
        }
    } catch (error) {
        console.error('Failed to fetch or display Care Plan:', error);
        document.querySelector('.accordion-item.color-green .accordion-content').textContent = 'Error loading goals.';
        document.querySelector('.accordion-item.color-blue .accordion-content').textContent = 'Error loading instructions.';
    }
}

async function fetchCareGaps() {
    try {
        const response = await fetch('/api/care_gaps_by_ehrid', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ ehrId: patientId })
        });
        if (!response.ok) throw new Error(`HTTP Error: ${response.status}`);

        const careGapsData = await response.json();




        if (careGapsData && careGapsData.careGaps && careGapsData.careGaps.length > 0) {


            const simplifiedData = await simplifyJsonWithAI(careGapsData, 'care_gaps');
            const finalData = reassembleJson(careGapsData, simplifiedData, 'care_gaps');

            console.log('simplfydata', simplifiedData);
            displayCareGaps(finalData.careGaps);
            loadingCaregaps.style.display = 'none';
        } else {
            document.querySelector('.accordion-item.color-red .accordion-content').textContent = 'No health reminders to display.';
        }
    } catch (error) {
        console.error('Failed to fetch or display Care Gaps:', error);
        document.querySelector('.accordion-item.color-red .accordion-content').textContent = 'Error loading health reminders.';
    }
}

function displayCareGaps(careGaps) {
    const container = document.querySelector('.accordion-item.color-red .accordion-content');
    if (!container) return;


    const groupedGaps = careGaps.reduce((acc, gap) => {
        const level = gap.confidenceLevel.toUpperCase();
        if (!acc[level]) acc[level] = [];
        acc[level].push(gap);
        return acc;
    }, {});

    const displayOrder = ['CONFIRMED', 'HIGH', 'MEDIUM', 'LOW'];
    let isFirstGroup = true;

    displayOrder.forEach(level => {
        if (groupedGaps[level] && groupedGaps[level].length > 0) {
            const heading = document.createElement('p');
            heading.className = 'cns';
            if (!isFirstGroup) heading.classList.add('mt');
            heading.textContent = level.charAt(0).toUpperCase() + level.slice(1).toLowerCase();
            container.appendChild(heading);

            const ul = document.createElement('ul');
            ul.className = 'timeline-red';

            groupedGaps[level].forEach(gap => {
                const li = document.createElement('li');
                li.className = 'timeline-item-red';
                li.textContent = gap.description;

                // commented tootip for caregaps for now 

                // if (gap.possibleCPTCodes && gap.possibleCPTCodes.length > 0) {
                //     li.classList.add('info-red');
                //     const tooltip = document.createElement('span');
                //     tooltip.className = 'tooltip';

                //     const tooltipTitle = document.createElement('p');
                //     tooltipTitle.className = 'tooltip-title';
                //     tooltipTitle.textContent = 'Possible CPT Codes:';
                //     tooltip.appendChild(tooltipTitle);

                //     const tooltipUl = document.createElement('ul');
                //     tooltipUl.className = 'tooltip-list';
                //     gap.possibleCPTCodes.forEach(cptCode => {
                //         const tooltipLi = document.createElement('li');
                //         tooltipLi.textContent = `${cptCode.description} (${cptCode.value})`;
                //         tooltipUl.appendChild(tooltipLi);
                //     });
                //     tooltip.appendChild(tooltipUl);
                //     li.appendChild(tooltip);
                // }
                ul.appendChild(li);
            });

            container.appendChild(ul);
            isFirstGroup = false;
        }
    });
}

function displayBehavioralGoals(goals) {
    const container = document.querySelector('.accordion-item.color-green .accordion-content');
    if (!container) return;
    container.innerHTML = '';

    const ul = document.createElement('ul');
    ul.className = 'timeline-green';

    goals.forEach(goalGroup => {
        goalGroup.item.forEach((itemText, index) => {
            const li = document.createElement('li');
            li.className = 'timeline-item-green info-green';
            li.textContent = itemText;

            const tooltipContent = goalGroup.reason[index] || 'No further details available.';



            li.addEventListener('mouseenter', (event) => {
                // Pass the title, content, and the element itself (li) to the function
                const rect = event.currentTarget.getBoundingClientRect();
                const top = rect.top;
                const left = rect.left;
                showTooltip('Details', tooltipContent, top, left);
            });

            // Add the event listener for hiding the tooltip
            li.addEventListener('mouseleave', () => {
                hideTooltip();
            });

            // const tooltip = document.createElement('span');
            // tooltip.className = 'tooltip';
            // tooltip.textContent = goalGroup.reason[index] || 'No further details available.';
            // li.appendChild(tooltip);

            ul.appendChild(li);
        });
    });

    if (ul.hasChildNodes()) {
        container.appendChild(ul);
    } else {
        container.textContent = "No goals available.";
    }
}
function showTooltip(titleText, contentText, top, left) {
    if (!tooltipTitleElement) {
        console.error("Tooltip title element not found!");
        return;
    }

    tooltipTitleElement.innerHTML = titleText;
    tooltipTextElement.innerHTML = contentText;
    tooltipElement.style.display = 'flex';

    // Get tooltip's height after content is set but before positioning
    const tooltipHeight = tooltipElement.offsetHeight + 10;

    // Position tooltip above the element by subtracting tooltip height
    tooltipElement.style.top = `${top - tooltipHeight}px`;
}

function hideTooltip() {
    if (!tooltipTitleElement) {
        console.error("Tooltip title element not found!");
        return; // Prevent errors if the element wasn't selected correctly
    }
    tooltipElement.style.display = 'none';
}

function displayEducationInstructions(instructions) {
    const container = document.querySelector('.accordion-item.color-blue .accordion-content');
    if (!container) return;
    container.innerHTML = '';

    const ul = document.createElement('ul');
    ul.className = 'timeline-blue';

    instructions.forEach(instructionGroup => {
        instructionGroup.item.forEach((itemText, index) => {
            const li = document.createElement('li');
            li.className = 'timeline-item-blue info-blue';
            li.textContent = itemText;

            const tooltipContent = instructionGroup.education[index] || 'No further details available.';

            // Add the event listener for showing the tooltip
            li.addEventListener('mouseenter', (event) => {
                // Pass the title, content, and the element itself (li) to the function
                const rect = event.currentTarget.getBoundingClientRect();
                const top = rect.top;
                const left = rect.left;
                showTooltip('Details', tooltipContent, top, left);
            });

            // Add the event listener for hiding the tooltip
            li.addEventListener('mouseleave', () => {
                hideTooltip();
            });
            ul.appendChild(li);
        });
    });

    if (ul.hasChildNodes()) {
        container.appendChild(ul);
    } else {
        container.textContent = "No instructions available.";
    }
}

async function fetchCalendarSlots(locationId, startDate, providerId) {
    try {
        // Show skeleton loading state
        const timeSlotsContainer = document.querySelector('.time-slots-container .time-dropdown');
        const timeSlotTrigger = document.querySelector('.time-slots-container .time-slot');

        if (timeSlotsContainer) {
            timeSlotsContainer.innerHTML = '<div class="time-slot skeleton">Loading...</div>';
        }
        if (timeSlotTrigger) {
            timeSlotTrigger.textContent = 'Loading...';
            timeSlotTrigger.classList.add('skeleton');
        }

        const response = await fetch('https://nexhealth-custom-server-1081644116068.us-central1.run.app/api/custom/slots', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                "locationId": locationId,
                "startDate": startDate,
                "providerId": providerId
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }

        const slotsData = await response.json();

        // Remove skeleton loading state
        if (timeSlotTrigger) {
            timeSlotTrigger.classList.remove('skeleton');
        }

        if (timeSlotsContainer) {
            timeSlotsContainer.innerHTML = ''; // Clear existing items

            if (slotsData.no_date) {
                // Show no slots available message
                const noSlotsMessage = document.createElement('div');
                noSlotsMessage.className = 'no-slots-message';
                noSlotsMessage.textContent = slotsData.message || 'No Slots available for this provider.';
                timeSlotsContainer.appendChild(noSlotsMessage);

                // Update trigger to show message
                if (timeSlotTrigger) {
                    timeSlotTrigger.textContent = 'No slots available';
                }
            } else if (slotsData.options) {
                // Get suggested time slots
                const suggestedSlots = getSuggestedTimeSlots(slotsData);

                // Create suggested slots section if we have suggestions
                if (suggestedSlots.length > 0) {
                    const suggestedSection = document.createElement('div');
                    suggestedSection.className = 'suggested-slots-section';

                    const separatorSuggest = document.createElement('div');
                    separatorSuggest.className = 'slots-separator';
                    separatorSuggest.innerHTML = '<span>Suggested Times</span>';
                    suggestedSection.appendChild(separatorSuggest);

                    const suggestedList = document.createElement('div');
                    suggestedList.className = 'suggested-slots-list';

                    suggestedSlots.forEach((slot, index) => {
                        const slotElement = document.createElement('span');
                        // Make the first suggested slot selected by default
                        slotElement.className = `time suggested-slot ${index === 0 ? 'selected' : ''}`;
                        slotElement.textContent = slot.text;
                        slotElement.dataset.slotId = slot.id;
                        suggestedList.appendChild(slotElement);

                        // If this is the first suggested slot, update the trigger
                        if (index === 0 && timeSlotTrigger) {
                            timeSlotTrigger.textContent = slot.text.split(' to ')[0];
                        }
                    });

                    suggestedSection.appendChild(suggestedList);
                    timeSlotsContainer.appendChild(suggestedSection);

                    // Add separator
                    const separator = document.createElement('div');
                    separator.className = 'slots-separator';
                    separator.innerHTML = '<span>All Available Times</span>';
                    timeSlotsContainer.appendChild(separator);
                }

                // Add all available slots
                slotsData.options.forEach((slot, index) => {
                    const slotElement = document.createElement('span');
                    // Only add selected class if there are no suggested slots
                    slotElement.className = `time dropdown-item ${(suggestedSlots.length === 0 && index === 0) ? 'selected' : ''}`;
                    slotElement.textContent = slot.text;
                    slotElement.dataset.slotId = slot.id;
                    timeSlotsContainer.appendChild(slotElement);

                    // If there are no suggested slots, set the first available slot as default
                    if (suggestedSlots.length === 0 && index === 0 && timeSlotTrigger) {
                        timeSlotTrigger.textContent = slot.text.split(' to ')[0];
                    }
                });

                // Add click event listeners to time slots
                timeSlotsContainer.querySelectorAll('.time').forEach(item => {
                    item.addEventListener('click', () => {
                        timeSlotsContainer.querySelectorAll('.time').forEach(slot => slot.classList.remove('selected'));
                        item.classList.add('selected');
                        if (timeSlotTrigger) {
                            timeSlotTrigger.textContent = item.textContent.split(' to ')[0];
                        }
                    });
                });
            }
        }

        return slotsData;
    } catch (error) {
        console.error('Error fetching calendar slots:', error);

        // Show error message in the calendar popup
        const timeSlotsContainer = document.querySelector('.time-slots-container .time-dropdown');
        const timeSlotTrigger = document.querySelector('.time-slots-container .time-slot');

        if (timeSlotsContainer) {
            timeSlotsContainer.innerHTML = '<div class="error-message">Error loading slots</div>';
        }

        if (timeSlotTrigger) {
            timeSlotTrigger.textContent = 'Error loading slots';
            timeSlotTrigger.classList.remove('skeleton');
        }

        return null;
    }
}


async function fetchTimeSlotsForMonth(month, year) {
    const daysInMonth = new Date(year, month + 1, 0).getDate();
    const today = new Date();
    const currentYear = today.getFullYear();
    const currentMonth = today.getMonth();
    const currentDay = today.getDate();

    // Remove has-slots class from all elements first
    document.querySelectorAll('.calendar-day').forEach(element => {
        element.classList.remove('has-slots');
    });

    // Create an array of promises for all dates in the month
    const promises = [];

    for (let day = 1; day <= daysInMonth; day++) {
        // Skip past dates
        if (year < currentYear ||
            (year === currentYear && month < currentMonth) ||
            (year === currentYear && month === currentMonth && day < currentDay)) {
            continue;
        }

        // Create date in EST timezone
        const date = new Date(year, month, day);
        // Convert to EST timezone string
        const estDate = new Date(date.toLocaleString('en-US', { timeZone: 'America/New_York' }));
        const formattedDate = estDate.toISOString().split('T')[0];

        // Add promise to array
        promises.push(
            fetchCalendarSlots(selectedLocationId, formattedDate, selectedProviderId)
                .then(slotsData => {
                    if (slotsData && !slotsData.no_date && slotsData.options && slotsData.options.length > 0) {
                        // Find the correct day element by matching the day number
                        const dayElements = document.querySelectorAll('.calendar-day:not(.inactive):not(.disabled)');
                        dayElements.forEach(element => {
                            if (parseInt(element.textContent) === day) {
                                element.classList.add('has-slots');
                            }
                        });
                    }
                    return { day, slotsData };
                })
                .catch(error => {
                    console.error(`Error fetching slots for ${formattedDate}:`, error);
                    return { day, error };
                })
        );
    }

    // Wait for all promises to resolve
    await Promise.all(promises);
}



// otp verifcation 


// ===============================================
//                TESTING SWITCH
//      Set to 'true' to test without a real API.
//      Set to 'false' to use your live cloud functions.
// ===============================================
const IS_TESTING_MODE = false;
// ===============================================

// This variable will hold our fake OTP during testing.
let mockOTP;

document.addEventListener('DOMContentLoaded', () => {

    let numberToVerify = '';

    // --- OTP API Functions ---
    async function sendOTP(phone) {
        if (IS_TESTING_MODE) {
            // --- TESTING LOGIC ---
            console.log("--- TESTING MODE: sendOTP ---");
            mockOTP = Math.floor(100000 + Math.random() * 900000).toString(); // Generate a random 6-digit code
            console.log(`✅ Correct Phone Number to Enter: ${phone}`);
            console.log(`🤫 Your One-Time Password is: ${mockOTP}`);

            // Simulate a network delay
            return new Promise(resolve => setTimeout(resolve, 500));
        } else {
            // --- REAL LOGIC ---
            try {
                const url = "/api/sendotp";
                await axios.post(url, { phoneNum: phone });
                console.log("sendOTP request sent successfully.");
            } catch (error) {
                throw new Error(error.response?.data?.message || 'Failed to send OTP.');
            }
        }
    }

    async function verifyOTP(phone, otp) {
        if (IS_TESTING_MODE) {
            // --- TESTING LOGIC ---
            console.log("--- TESTING MODE: verifyOTP ---");
            console.log(`Verifying against stored mock OTP: ${mockOTP}`);

            // Simulate a network delay
            return new Promise((resolve, reject) => {
                setTimeout(() => {
                    if (otp === mockOTP) {
                        console.log("✅ Mock OTP Correct!");
                        resolve();
                    } else {
                        console.error("❌ Mock OTP Incorrect!");
                        reject(new Error("Invalid verification code."));
                    }
                }, 500);
            });
        } else {
            // --- REAL LOGIC ---
            try {
                const url = "/api/verifyotp";
                const response = await axios.post(url, { phoneNum: phone, otp: otp });
                console.log('whatis responsein verifyotp', response.data)

            } catch (error) {
                throw new Error(error.response?.data?.message || 'Invalid verification code.');
            }
        }
    }

    const phoneEntryScreen = document.getElementById('phone-entry-screen');
    const otpEntryScreen = document.getElementById('otp-entry-screen');


    // --- Get Element References (No changes here) ---
    const verificationFlow = document.getElementById('verification-flow');
    const carePlanContent = document.getElementById('care-plan-content');
    // ... all your other getElementById calls ...
    const phoneForm = document.getElementById('phone-form');
    const otpForm = document.getElementById('otp-form');
    const phoneNumberInput = document.getElementById('phone-number');
    const otpCodeInput = document.getElementById('otp-code');
    const sendOtpBtn = document.getElementById('send-otp-btn');
    const verifyOtpBtn = document.getElementById('verify-otp-btn');
    const loadingSpinner = document.getElementById('loading-spinner');
    const errorMessageDiv = document.getElementById('error-message');

    // --- Event Listeners (No changes here, they will now use the mock functions) ---
    phoneForm.addEventListener('submit', async (event) => {
        event.preventDefault();
        const userEnteredPhoneNumber = phoneNumberInput.value.trim();

        if (!userEnteredPhoneNumber) {
            showError('Please enter a phone number.');
            return;
        }
        if (typeof phoneNumber === 'undefined' || phoneNumber === null) {
            showError('System error: Phone number not loaded.');
            return;
        }

        const normalizedUserNumber = userEnteredPhoneNumber.replace(/\D/g, '');
        const normalizedCorrectNumber = phoneNumber.replace(/\D/g, '');
        // const normalizedCorrectNumber = "**********";


        if (normalizedUserNumber !== normalizedCorrectNumber) {
            showError('This phone number does not match the one on file for this patient.');
            return;
        }

        toggleLoading(true, sendOtpBtn);
        try {
            await sendOTP(normalizedUserNumber);

            numberToVerify = normalizedUserNumber;
            phoneEntryScreen.style.display = 'none';
            otpEntryScreen.style.display = 'block';
            otpCodeInput.focus();
            errorMessageDiv.style.display = 'none';
        } catch (error) {
            showError(error.message);
        } finally {
            toggleLoading(false, sendOtpBtn);
        }
    });

    otpForm.addEventListener('submit', async (event) => {
        event.preventDefault();
        const otpCode = otpCodeInput.value.trim();
        if (!otpCode) {
            showError('Please enter the verification code.');
            return;
        }
        toggleLoading(true, verifyOtpBtn);
        try {
            await verifyOTP(numberToVerify, otpCode);
            verificationFlow.style.display = 'none';
            carePlanContent.style.display = 'block';
        } catch (error) {
            showError(error.message);
        } finally {
            toggleLoading(false, verifyOtpBtn);
        }
    });

    // --- Helper Functions (No changes here) ---
    function toggleLoading(isLoading, button) {
        errorMessageDiv.style.display = 'none';
        loadingSpinner.style.display = isLoading ? 'block' : 'none';
        if (button) button.disabled = isLoading;
    }

    function showError(message) {
        errorMessageDiv.textContent = message;
        errorMessageDiv.style.display = 'block';
    }



});