/* Base Styles and Variables */
:root {
    --primary-color: #2a7d8c;
    --secondary-color: #3a9eac;
    --light-color: #ffffff;
    --dark-color: #333;
    --gray-color: #888;
    --light-gray: #eee;
    --border-radius: 10px;
    --shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
    --header-bg: #f4f9fa;
    --content-bg: #fff;
}

/* Reset & Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: TodaySansNow Pro, sans-serif;
    line-height: 1.6;
    color: var(--dark-color);
    background-color: var(--light-color);
    font-size: 16px;
}

/* Container & Layout */
.container {
    width: 100%;
    max-width: 500px; /* or whatever width you prefer */
    margin: 0 auto;
    overflow: hidden;
}


/* Header Styles */
header {
    text-align: center;
}

h1 {
    color: var(--primary-color);
    font-size: 2.2rem;
    margin-bottom: 10px;
}

.head{
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 28px;
    box-shadow: 4px 13px 21px -12px #00000014;
    padding: 22px 16px;

}

/* Patient Info Bar Styles */
.patient-info-bar {
    padding: 0 26px;
    margin-bottom: 20px;
}

/* This container holds the two pairs side by side */
.patient-info-left {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    row-gap: 12px;
}

.patient-info-left .inner-wrapper{
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
}

/* Each pair has minimal gap between label and value */
.patient-pair {
    display: flex;
    align-items: flex-end;
    column-gap: 10px;
    width: 100%;
}

/* Label styling */
.patient-label {
    font-size: 0.9rem;
    font-weight: 500;
    color: #666; 
}

/* Value styling */
.patient-value {
    font-family: TodaySansNow Pro;
    font-weight: 400;
    font-size: 20px;
    line-height: 100%;
    letter-spacing: 0%;
    vertical-align: middle;
    color: #1E3565;
}

.patient-value b{
    font-weight: 500;
}

/* Loading Indicator */
#loading {
    text-align: center;
    padding: 40px;
    font-size: 1.2rem;
    color: var(--gray-color);
}

/* Accordion Styles */
.accordion-container {
    width: 100%;
    padding: 0 26px;
}

.accordion-section {
    margin-bottom: 12px;
    border-radius: var(--border-radius);
    overflow: hidden;
    border: 1px solid #CDCDCD
}

.accordion-section .accordion-section {
    margin-bottom: 0;
}

.accordion-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    cursor: pointer;
    transition: var(--transition);
}

.accordion-header h2 {
    font-family: TodaySansNow Pro;
    font-weight: 500;
    font-size: 24px;
    line-height: 100%;
    letter-spacing: 0%;
    vertical-align: middle;
    color: #1E3565;
}

.accordion-header h3{
    font-family: TodaySansNow Pro;
    font-weight: 400;
    font-size: 24px;
    line-height: 100%;
    letter-spacing: 0%;
    vertical-align: middle;
    color: #1E3565;
}

.toggle-icon {
    transition: var(--transition);
}

.accordion-section.active .toggle-icon {
    transform: rotate(180deg);
}

.accordion-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.5s ease;
}

.accordion-content li{
    font-family: TodaySansNow Pro;
    font-weight: 400;
    font-size: 20px;
    line-height: 100%;
    letter-spacing: 0%;
    vertical-align: middle;
}

.accordion-section.active .accordion-content {
    max-height: none; /* Remove the height restriction */
    padding: 20px;
    padding-top: 0;
    overflow-y: auto; /* Enable vertical scrolling if needed */
}

/* Priorities List */
.priorities-list {
    list-style: none;
}

.priority-item {
    padding: 12px 16px;
    background-color: #FFF5F5;
    margin-bottom: 12px;
    border-radius: var(--border-radius);
    border: 0.4px solid #F5BABA;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    transition: var(--transition);
    color: #7D1010;
}

.info-icon {
    color: var(--gray-color);
    margin-left: 10px;
}

/* Provider Plan Styles */
.provider-plan-subsections {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.provider-subsection {
    background-color: #fff;
    border-radius: var(--border-radius);
    padding: 16px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.provider-subsection h3 {
    color: var(--primary-color);
    margin-bottom: 12px;
    font-size: 1.1rem;
    border-bottom: 1px solid var(--light-gray);
    padding-bottom: 8px;
}

.value-reason-pair {
    display: flex;
    margin-bottom: 8px;
    padding: 12px 18px;
    border-radius: var(--border-radius);
    background-color: var(--light-color);
    transition: var(--transition);
    cursor: pointer;
    align-items: center;
    background-color: #F4F7FF;
    color: #1E3565;
    border: 0.4px solid #ADC0E5;
}

.value-reason-pair.continue{
    background-color: #F4F7FF;
    color: #1E3565;
    border: 0.4px solid #ADC0E5;
}

.value-reason-pair.add{
    background-color: #F4FBF3;
    color: #185E09F2;
    border: 0.4px solid #95DBBB;
}

.value-reason-pair.change{
    background-color: #FFEBC9;
    color: #1E3565;
    border: 0.4px solid #fcc972;
}

.value-reason-pair.remove{
    background-color: #FFF5F5;
    color: #7D1010;
    border: 0.4px solid #F5BABA;
}

.value-reason-pair .provider-name {
    display: none;
    position: absolute;
    right: 18px;
    top: 50%;
    transform: translateY(-50%);
    width: 140px;
    padding: 2px 8px;
    border-radius: 4px;
    background-color: #fff;
    box-shadow: var(--shadow);
    border-radius: 4px;
}

.value-reason-pair .provider-name.active {
    display: block;
}

.value-text {
    flex-grow: 1;
    font-family: TodaySansNow Pro;
    font-weight: 400;
    font-size: 20px;
    line-height: 100%;
    letter-spacing: 0%;
}

/* Patient Plan Styles */
.patient-plan-container {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.patient-section {
    background-color: #fff;
    border-radius: var(--border-radius);
    padding: 16px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.patient-section h3 {
    color: var(--primary-color);
    margin-bottom: 12px;
    font-size: 1.1rem;
    border-bottom: 1px solid var(--light-gray);
    padding-bottom: 8px;
}

.patient-list {
    list-style: none;
}

.patient-item {
    padding: 12px 18px;
    margin-bottom: 8px;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    justify-content: space-between; 
    align-items: center;
    background-color: #F6F5FE;
    color: #575757F2;
    border: 0.4px solid #C6C0F2
}

/* Timeline Grid Styles */
.timeline-container {
   margin-top: 8px;
}

.month-card {
    background-color: #fff;
    border-radius: var(--border-radius);
    padding: 0 16px;
    display: flex;
    flex-direction: row;
}

.month-header {
    font-family: TodaySansNow Pro;
    font-weight: 400;
    font-size: 20px;
    line-height: 100%;
    letter-spacing: 0%;
    vertical-align: middle;
    color: #676767;
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    flex-direction: column;
    width: calc(130px - 18px);
    flex: 0 0 calc(130px - 18px);
    border-right: 1px solid #1E3565;
    margin-right: 18px;
    padding-right: 12px;
    padding-bottom: 30px;
    position: relative;
}

.month-card:first-child .month-header{
    padding-top: 12px;
}

.month-header::before{
    content: "";
    display: block;
    width: 15px;
    height: 15px;
    background-color: #fff;
    border-radius: 50%;
    border: 1px solid #1E3565;
    position: absolute;
    top: 1px;
    right: -9px;
}

.month-card:first-child .month-header::before{
    top: 12px;
}

.month-header button{
    background: #379D90;
    border: none;
    font-family: TodaySansNow Pro;
    font-weight: 400;
    font-size: 20px;
    line-height: 100%;
    letter-spacing: 0%;
    vertical-align: middle;
    color: #FFFFFF;
    border-radius: 10px;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    padding: 8px 14px;
    margin-top: 12px;
}

.month-header button:disabled{
    background-color: lightgray;
    cursor: not-allowed;
}

.month-header button img{
    width: 17px;
    height: auto;
    margin-left: 8px;
}

.task-list {
    list-style: none;
    padding-bottom: 30px;
    width: 100%;
}

.task-item {
    padding: 16px;
    padding-right: 10px;
    margin-bottom: 8px;
    background-color: #F2FAF9;
    border: 0.4px solid #A2E1D9;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);

    /* --- NEW: Add Flexbox for layout --- */
    display: flex;
    justify-content: space-between; /* Pushes icon to the right */
    align-items: center; /* Vertically aligns text and icon */
    
    
}

/* Tooltip Styles */
.tooltip {
    position: fixed;
    top: 0;
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    transform: translateX(50%);
    right: 50%;
    width: 400px;
}

.tooltip-content {
    width: 380px;
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
    padding: 20px;
    position: relative;
}

.tooltip-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid var(--light-gray);
}

.tooltip-header h3 {
    color: #1E3565;
    font-family: TodaySansNow Pro;
    font-weight: 500;
    font-size: 22px;
    line-height: 100%;
    letter-spacing: 0%;
    vertical-align: middle;
}

.close-tooltip {
    background: none;
    border: none;
    color: var(--gray-color);
    font-size: 1.2rem;
    cursor: pointer;
    transition: var(--transition);
}

.close-tooltip:hover {
    color: var(--dark-color);
}

#tooltip-text {
    font-family: TodaySansNow Pro;
    font-weight: 400;
    font-size: 18px;
    line-height: 100%;
    letter-spacing: 0%;
    vertical-align: middle;    
}

/* Responsive Styles */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    h1 {
        font-size: 1.8rem;
    }
    
    .accordion-header h2 {
        font-size: 1.1rem;
    }
    
    .tooltip-content {
        width: 95%;
    }
    
    .timeline-container {
        grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    }
}

@media (max-width: 480px) {
    h1 {
        font-size: 1.5rem;
    }
    
    .accordion-header {
        padding: 12px 16px;
    }
    
    .accordion-header h2 {
        font-size: 1rem;
    }
    
    .accordion-section.active .accordion-content {
        padding: 16px;
    }
    
    .timeline-container {
        grid-template-columns: 1fr;
    }
}

/* Chrome Extension Specific Styles */
@media (max-width: 400px) {
    body {
        font-size: 14px;
    }
    
    .container {
        padding: 8px;
    }
    
    h1 {
        font-size: 1.3rem;
        margin-bottom: 8px;
    }
    
    .accordion-section {
        margin-bottom: 10px;
    }
}

/* Make sure accordion content is hidden by default */
.accordion-content {
    display: none;
}

/* Show content when parent has active class */
.accordion-section.active > .accordion-content {
    display: block;
}

/* Same for subfields */
.subfield .accordion-content {
    display: none;
}

.subfield.active > .accordion-content {
    display: block;
}

/* Search Form Styles */
.search-form {
    display: flex;
    justify-content: center;
    position: relative;
}

.search-form input[type="text"] {
    padding: 8px 12px;
    border: 0.5px solid #BDCBE9;
    border-radius: 10px 0px 0px 10px;
    font-family: TodaySansNow Pro;
    font-weight: 400;
    font-size: 18px;
    line-height: 100%;
    letter-spacing: 0%;
    vertical-align: middle;
    color: #1E3565;
    width: 200px;
    transition: var(--transition);
    background-color: #D5E3FF73;
    padding-left: 34px;
}

.search-form::before{
    content: "";
    display: block;
    position: absolute;
    left: 14px;
    top: 50%;
    transform: translateY(-50%);
    width: 12px;
    height: 12px;
    background-image: url("../images/search-icon.png");
    background-size: contain;
    background-repeat: no-repeat;
}

.search-form input[type="text"]::placeholder {
    font-family: TodaySansNow Pro;
    font-weight: 400;
    font-size: 18px;
    line-height: 100%;
    letter-spacing: 0%;
    vertical-align: middle;
    color: #1E3565;
}

.search-form input[type="text"]:focus {
    outline: none;
    border-color: #88a8ee;
}

.search-form button {
    padding: 8px 16px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 0px 10px 10px 0px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: var(--transition);
    font-family: TodaySansNow Pro;
    font-weight: 400;
    font-size: 18px;
    line-height: 100%;
    letter-spacing: 0%;
    text-align: center;
    vertical-align: middle;
    color: #FFFFFF;
    background-color: #7594D8;
}

.search-form button:hover {
    background-color: #537edb;
}

.search-form button:active {
    transform: translateY(1px);
}

/* Responsive adjustments */
@media (max-width: 480px) {
    .search-form {
        flex-direction: row;
        gap: 6px;
    }

    .search-form input[type="text"] {
        width: 120px;
        padding: 6px 10px;
    }

    .search-form button {
        padding: 6px 12px;
    }
}

/* Skeleton Loading Animation */
@keyframes shimmer {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: 200px 0;
    }
}

.skeleton {
    background: linear-gradient(90deg, 
        #f0f0f0 25%, 
        #e0e0e0 50%, 
        #f0f0f0 75%
    );
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite linear;
    border-radius: 4px;
    min-width: 90px;
    height: 30px;
    display: inline-block;
}

.patient-value.skeleton {
    color: transparent;
}

.selected-provider.skeleton {
    min-width: 120px;
    color: transparent;
    background: linear-gradient(90deg, 
        #f0f0f0 25%, 
        #e0e0e0 50%, 
        #f0f0f0 75%
    );
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite linear;
}

.medication-diagnosis{
    border-radius: 4px;
    padding: 6px 10px;
    font-family: TodaySansNow Pro;
    font-weight: 500;
    font-size: 20px;
    line-height: 100%;
    letter-spacing: 0%;
    color: #1E3565;
}

.feedback-btn{
    font-family: TodaySansNow Pro;
    font-weight: 400;
    font-size: 20px;
    line-height: 100%;
    letter-spacing: 0%;
    text-align: center;
    vertical-align: middle;
    color: #1E3565;
    background-color: #9ACF8C;
    border-radius: 10px;
    border: none;
    padding: 9px 16px;
    margin-left: 10px;
    display: none;
}

.patient-name{
    font-family: TodaySansNow Pro;
    font-weight: 400;
    font-size: 30px;
    line-height: 100%;
    letter-spacing: 0%;
    vertical-align: middle;
    text-align: left;
    color: #1E3565;
    max-width: 220px;
}

.gen-age-wrapper{
    font-family: TodaySansNow Pro;
    font-weight: 400;
    font-size: 20px;
    line-height: 100%;
    letter-spacing: 0%;
    color: #1E3565E0;
}

.patient-pair .just-a-dot{
    width: 4px;
    height: 4px;
    background-color: #1E3565;
    border-radius: 50%;
    align-self: center;
    margin-top: 8px;
}

/* Dropdown styles */
.dropdown-trigger {
    cursor: pointer;
    position: relative;
    padding: 6px 8px;
    padding-right: 28px;
    background-color: #5C78B3;
    font-family: TodaySansNow Pro;
    font-weight: 400;
    font-size: 16px;
    line-height: 100%;
    letter-spacing: 0%;
    vertical-align: middle;
    color: #FFFFFF;
    border-radius: 10px;
}

.dropdown-trigger::after {
    content: '\f078';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    font-size: 0.7em;
    position: absolute;
    right: 6px;
    top: 50%;
    transform: translateY(-50%);
}

.dropdown-content {
    display: none;
    position: absolute;
    background-color: white;
    min-width: 85px;
    z-index: 1;
    border-radius: 8.55px;
    margin-top: 5px;
    box-shadow: 0px 0px 14.24px 2.85px #BFBFBF40;
    right: 0;
    padding: 5px;
    max-height: 150px;
    overflow: auto;
}

.dropdown-content.active {
    display: block;
}

.provider-dropdown{
    top: 28px;
}

.dropdown-item {
    padding: 4px 5px;
    text-decoration: none;
    display: block;
    cursor: pointer;
    font-family: TodaySansNow Pro;
    font-weight: 500;
    font-size: 14px;
    line-height: 15.02px;
    letter-spacing: 0%;
    text-align: center;
    vertical-align: middle;
    color: #1E3565;
    background-color: #DFE7F5;
    margin-bottom: 2px;
    border-radius: 3.75px;
}

.num-days-container, .time-slots,.provider-container {
    position: relative;
    font-family: TodaySansNow Pro;
    font-weight: 400;
    font-size: 22px;
    line-height: 100%;
    letter-spacing: 0%;
}


.followup-wrapper{
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 0 26px;
    margin-bottom: 28px;
}

.book-btn, .book-month-btn,
.share-btn {
    font-family: TodaySansNow Pro;
    font-weight: 400;
    font-size: 20px;
    line-height: 100%;
    letter-spacing: 0%;
    vertical-align: middle;
    color: #F5FBF4;
    background-color: #395695;
    border-radius: 10px;
    padding: 9px 12px;
    padding-right: 34px;
    border: none;
    cursor: pointer;
    background-image: url("../images/calender-icon.png");
    background-repeat: no-repeat;
    background-position: right 10px center;
    background-size: 17px;
    transition: background-color 0.3s;
}

.share-btn {
    background-image: unset;
    padding-right: 12px;
}

.share-btn:disabled {
    background-color: #e0e0e0;
    cursor: not-allowed;
}

.share-btn.skeleton {
    background-color: #e0e0e0;
    color: transparent;
    position: relative;
    overflow: hidden;
}

.share-btn.skeleton::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: skeleton-loading 1.5s infinite;
}

@keyframes skeleton-loading {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

.book-btn:disabled, .book-month-btn:disabled {
    background-color: #e0e0e0;
    cursor: not-allowed;
}

.book-btn.skeleton, .book-month-btn.skeleton {
    background-color: #e0e0e0;
    background-image: none;
    color: transparent;
    position: relative;
    overflow: hidden;
}

.book-btn.skeleton::after, .book-month-btn.skeleton::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: skeleton-loading 1.5s infinite;
}

.patient-info-bar .book-btn{
    margin-left: auto;
}

.provider-container{
    display: flex;
    align-items: center;
    justify-content: flex-end;
    width: 100%;
    position: relative;
}

.selected-provider{
    margin-left: 8px;
}

/* Google Sign-in styles */
#login-section {
    text-align: center;
    padding: 60px 20px;
    max-width: 400px;
    margin: 80px auto;
    background-color: var(--light-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

#login-section h1 {
    color: #1E3565;
    font-size: 36px;
    margin-bottom: 15px;
    font-weight: 500;
}

.sign-in-text {
    color: #1E3565;
    font-size: 20px;
    margin-bottom: 30px;
}

.google-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #1E3565;
    color: white;
    border-radius: 8px;
    padding: 10px 20px;
    width: 140px;
    margin: 0 auto;
    cursor: pointer;
    transition: background-color 0.3s;
}

.google-btn:hover {
    background-color: #152a4f;
}

.google-btn img {
    width: 24px;
    height: 24px;
    margin-right: 10px;
}

.google-btn span {
    font-size: 18px;
    font-weight: 500;
}

/* Profile Button and Popup Styles */
.profile-container {
    position: relative;
    margin-left: 10px;
    display: flex;
    align-items: center;
}

.profile-btn {
    width: 36px;
    height: auto;
    border-radius: 50%;
    cursor: pointer;
    border: none;
    padding: 0;
    background: none;
}

.profile-popup {
    display: none;
    position: absolute;
    top: 45px;
    right: 0;
    width: 200px;
    background-color: #1E3565;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    color: white;
    overflow: hidden;
}

.popup-arrow {
    position: absolute;
    top: -10px;
    right: 13px;
    width: 0;
    height: 0;
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
    border-bottom: 10px solid #1E3565;
}

.popup-content {
    padding: 20px;
    text-align: center;
}

.user-name {
    font-family: TodaySansNow Pro;
    font-size: 24px;
    margin-bottom: 20px;
    color: white;
}

.profile-popup .signout-btn {
    background-color: white;
    color: #1E3565;
    border: none;
    border-radius: 20px;
    padding: 8px 20px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.3s;
    width: 100%;
}

.profile-popup .signout-btn:hover {
    background-color: #f0f0f0;
}

.profile-popup.active {
    display: block;
}

/* Calendar Popup Styles */
.calendar-popup {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.calendar-container {
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    width: 400px;
    max-width: 90%;
    overflow: hidden;
    position: relative;
    padding-top: 24px;
}

.calendar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid #eee;
}

.calendar-header h3 {
    margin: 0;
    color: #1E3565;
    font-weight: 500;
}

.calendar-header .header-right {
    display: flex;
    align-items: center;
    gap: 10px;
}

.calendar-header button {
    background: none;
    border: none;
    font-size: 18px;
    color: #1E3565;
    cursor: pointer;
    padding: 5px 10px;
}

.calendar-header .close-calendar {
    font-size: 32px;
    padding: 0;
    position: absolute;
    top: 4px;
    right: 14px;
    cursor: pointer;
}

.calendar-grid {
    display: flex;
    padding: 10px;
}

.calendar-days{
    display: flex;
    flex-direction: column;
    flex: 1;
}

.week-wrapper,
.calendar-days-wrapper {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 5px;
}

.calendar-days-wrapper{
    flex: 1;
}

.day-header {
    text-align: center;
    font-weight: 500;
    color: #1E3565;
    padding: 8px 0;
}

.calendar-day {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 22px;
    border-radius: 50%;
    cursor: pointer;
    color: #333;
}

.calendar-day.inactive {
    color: #ccc;
}

.calendar-day:hover:not(.inactive) {
    background-color: #f0f0f0;
}

.time-slots-container {
    flex: 1;
    padding: 0 10px;
}

.time-slots-container .time-dropdown{
    height: 150px;
    overflow-y: auto;
}

.time-slots-container h4 {
    margin: 0 0 10px 0;
    color: #1E3565;
    font-weight: 500;
    font-size: 16px;
    line-height: 100%;
    letter-spacing: 0%;
}

.time-slots-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.time-slot-item {
    padding: 10px;
    border-radius: 8px;
    background-color: #f0f0f0;
    cursor: pointer;
}

.time-slot-item.selected {
    background-color: #d1e7d1;
}

.time-slot-item:hover:not(.selected) {
    background-color: #e8e8e8;
}

.calendar-footer {
    padding: 15px 20px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    border-top: 1px solid #eee;
}

.confirm-btn {
    background-color: #379D90;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 8px 16px;
    font-size: 14px;
    cursor: pointer;
}

.confirm-btn:hover {
    background-color: #2a8a7d;
}

.confirm-btn.loading {
    opacity: 0.7;
    cursor: not-allowed;
}

.time-slots-container .dropdown-item,
.time-slots-container .suggested-slot{
    font-weight: 500;
    font-size: 16px;
    line-height: 15.02px;
    letter-spacing: 0%;
    text-align: center;
    vertical-align: middle;
    border-radius: 3.75px;
    background-color: #fff;
    padding: 6px 0px;
}

.time-slots-container .dropdown-item.selected,
.time-slots-container .suggested-slot.selected{
    color: #115D00;
    background-color: #115D0030;
}

.loc-name{
    color: #1E3565;
    font-weight: 500;
    font-size: 16px;
    line-height: 100%;
    letter-spacing: 0%;
    text-align: right;
    margin-right: 5px;
    margin-top: 4px;
}

.time-slots-container .provider-container{
    margin-bottom: 14px;
}

.info-icon::before {
    content: "";
    display: block;
    width: 16px;
    height: 16px;
    background-image: url("../images/info-calendar.png");
    background-size: contain;
    background-repeat: no-repeat;
    background-size: contain;
}

.month-card .task-list .info-icon::before {
    background-image: url("../images/info-calendar.png");
}

.priority-item .info-icon::before {
    background-image: url("../images/info-red.png");
}

.patient-item .info-icon::before {
    background-image: url("../images/info-pt.png");
}

.value-reason-pair .info-icon::before{
    background-image: url("../images/info-continue.png");
}

.value-reason-pair.continue .info-icon::before{
    background-image: url("../images/info-continue.png");
}

.value-reason-pair.add .info-icon::before{
    background-image: url("../images/info-add.png");
}

.value-reason-pair.change .info-icon::before{
    background-image: url("../images/info-red.png");
}

.value-reason-pair.remove .info-icon::before{
    background-image: url("../images/info-red.png");
}

.caregap-title h3{
    font-family: TodaySansNow Pro;
    font-weight: 500;
    font-size: 20px;
    line-height: 100%;
    letter-spacing: 0%;
    color: #1E3565;
    padding: 6px 10px;
}

.caregap-section{
    margin-bottom: 20px;
}

.popup-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 9999;
    justify-content: center;
    align-items: center;
}

.popup-overlay .popup-content {
    background-color: white;
    padding: 20px;
    border-radius: 8px;
    min-width: 300px;
    max-width: 90%;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.popup-overlay .popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.popup-overlay .popup-header h3 {
    margin: 0;
    color: #1E3565;
    font-weight: 500;
}

.popup-overlay .close-popup {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #000;
}

.popup-overlay .popup-body {
    margin-bottom: 20px;
    color: #333;
}

.popup-overlay .popup-footer {
    text-align: right;
}

.popup-overlay .popup-ok-btn {
    background-color: #379D90;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 8px 16px;
    font-size: 14px;
    cursor: pointer;
}

.provider-search-container {
    padding: 8px;
    border-bottom: 1px solid #e0e0e0;
    position: sticky;
    top: 0;
    background: white;
    z-index: 1;
}

.provider-search {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    font-size: 14px;
    outline: none;
    transition: border-color 0.2s;
}

.provider-search:focus {
    border-color: #1E3565;
}

.provider-search::placeholder {
    color: #999;
}

/* Adjust provider dropdown to accommodate search */
.provider-dropdown {
    max-height: 300px;
    overflow-y: auto;
}

/* Ensure provider items have proper spacing */
.provider-dropdown .provider {
    padding: 8px 12px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.provider-dropdown .provider:hover {
    background-color: #f5f5f5;
} 

.reason-input {
    margin: 15px 0;
    padding: 0 20px;
    padding-left: 0;
    width: 100%;
}

.reason-input input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.reason-input input:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
}

.calendar-day.has-slots {
    position: relative;
    background-color: #F4FBF3;
    border-color: #F4FBF3;
}

.calendar-day.has-slots::after {
    content: '';
    position: absolute;
    bottom: 2px;
    left: 50%;
    transform: translateX(-50%);
    width: 4px;
    height: 4px;
    background-color: #185E09F2;
    border-radius: 50%;
}

.calendar-day.has-slots:hover {
    background-color: #65BD5157;
}

.calendar-day.selected {
    color: #333;
    background-color: #65BD5157;
}

/* Suggested Time Slots Styles */
.suggested-slots-section {
    padding: 10px 0;
    border-bottom: 1px solid #eee;
    margin-bottom: 10px;
}

.suggested-slots-section h4 {
    font-family: 'Inter', sans-serif;
    font-size: 14px;
    font-weight: 500;
    color: #1a1a1a;
    margin: 0 0 8px 0;
    padding: 0 15px;
}

.suggested-slots-list {
    display: flex;
    flex-direction: column;
}

.suggested-slot {
    cursor: pointer;
    transition: all 0.2s ease;
    color: #1E3565;
}

.slots-separator {
    position: relative;
    text-align: center;
    margin: 8px 0;
    padding: 0 15px;
}

.slots-separator::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background-color: #eee;
}

.slots-separator span {
    position: relative;
    background-color: white;
    padding: 0 10px;
    color: #666;
    font-size: 12px;
}

/* Update existing time slot styles */
.time.dropdown-item {
    cursor: pointer;
    transition: all 0.2s ease;
}

.slots-separator:first-of-type{
    margin-top: 0;
}